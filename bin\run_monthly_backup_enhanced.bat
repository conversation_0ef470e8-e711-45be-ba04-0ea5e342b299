@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Monthly Backup System - Fixed Version           S4NG-7F
REM ===============================================================
REM Fixed monthly backup system with proper implementation:
REM - Consistent delayed expansion usage
REM - Proper error recovery mechanisms
REM - Health checks and dependency validation
REM - Fallback configuration sources
REM - Comprehensive monitoring and alerting
REM - Security hardening and input validation
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM Configuration with proper validation
set "PYTHON_CMD=python"
set "MONTHLY_BACKUP_SCRIPT=scripts\enhanced_monthly_backup_processor.py"

REM Initialize logging with consistent expansion
call :get_timestamp timestamp
call :create_log_directory "monthly" "!timestamp!" log_dir
set "LOG_FILE=!log_dir!\monthly_backup_!timestamp!.log"
call :init_logging "!LOG_FILE!" "MonthlyBackup" %LOG_LEVEL_INFO%

call :log_operation_start "Monthly Backup System"

REM Parameter processing with proper validation
set "MODE=monthly"
set "MONTH_NAME="
set "YEAR=2025"
set "DRY_RUN=false"
set "VERBOSE=false"
set "FORCE_RECOVERY=false"
set "SKIP_HEALTH_CHECKS=false"

REM Process command line arguments with validation
call :process_monthly_arguments %*
if !ERRORLEVEL! NEQ 0 exit /b !ERRORLEVEL!

goto :main_monthly_process

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_monthly_arguments
REM Get first two positional arguments
set "MONTH_NAME=%~1"
set "YEAR=%~2"

REM Validate required parameters
call :validate_required_param "!MONTH_NAME!" "month name"
if !ERRORLEVEL! NEQ 0 (
    call :show_monthly_help
    exit /b %ERROR_INVALID_PARAM%
)

call :validate_safe_string "!MONTH_NAME!" "month name"
if !ERRORLEVEL! NEQ 0 exit /b %ERROR_INVALID_PARAM%

if "!YEAR!"=="" set "YEAR=2025"
call :validate_safe_string "!YEAR!" "year"
if !ERRORLEVEL! NEQ 0 exit /b %ERROR_INVALID_PARAM%

REM Process optional arguments
shift & shift
:parse_monthly_args
if "%~1"=="" goto monthly_args_done

if /i "%~1"=="--dry-run" (
    set "DRY_RUN=true"
    call :log_info "Dry run mode enabled"
    shift & goto parse_monthly_args
)

if /i "%~1"=="--verbose" (
    set "VERBOSE=true"
    set "CURRENT_LOG_LEVEL=%LOG_LEVEL_DEBUG%"
    call :log_info "Verbose mode enabled"
    shift & goto parse_monthly_args
)

if /i "%~1"=="--force-recovery" (
    set "FORCE_RECOVERY=true"
    call :log_info "Force recovery mode enabled"
    shift & goto parse_monthly_args
)

if /i "%~1"=="--skip-health-checks" (
    set "SKIP_HEALTH_CHECKS=true"
    call :log_info "Health checks will be skipped"
    shift & goto parse_monthly_args
)

if /i "%~1"=="help" goto show_monthly_help
if /i "%~1"=="--help" goto show_monthly_help

call :log_warning "Unknown parameter: %~1"
shift & goto parse_monthly_args

:monthly_args_done
goto :eof

REM ===============================================================
REM MAIN MONTHLY PROCESS
REM ===============================================================

:main_monthly_process
echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: !MONTH_NAME! !YEAR!
echo Mode: Production-Ready with Fixes
echo Log File: !LOG_FILE!
if "!DRY_RUN!"=="true" echo Mode: DRY RUN (validation only)
if "!FORCE_RECOVERY!"=="true" echo Mode: FORCE RECOVERY enabled
echo Current time: %date% %time%
echo ===============================================================
echo.

call :log_system_info

REM Configuration discovery with proper error handling
call :log_info "Starting configuration validation..."
call :find_table_config TABLE_CONFIG_PATH
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration not found"
    exit /b %ERROR_CONFIG%
)

call :validate_table_config "!TABLE_CONFIG_PATH!"
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration validation failed"
    exit /b %ERROR_CONFIG%
)

call :log_info "Using table configuration: !TABLE_CONFIG_PATH!"

REM Health checks with proper error handling
if not "!SKIP_HEALTH_CHECKS!"=="true" (
    call :log_info "Starting system health checks..."
    echo Performing system health checks...

    REM Check Python availability
    call :safe_execute "call :check_python_available" "Python availability check"
    if !ERRORLEVEL! NEQ 0 (
        call :handle_error %ERROR_PYTHON% "Python not available or not in PATH"
        exit /b %ERROR_PYTHON%
    )
    echo ✅ Python availability check passed

    REM Check disk space
    call :log_info "Checking disk space..."
    call :safe_execute "call :check_disk_space 5" "Disk space check"
    if !ERRORLEVEL! NEQ 0 (
        call :log_warning "Low disk space detected (less than 5GB free)"
        REM Continue with backup but log the warning
    ) else (
        echo ✅ Disk space check passed
    )

    echo ✅ All health checks completed
    call :log_info "All health checks completed"
) else (
    echo ⚠️ Health checks skipped (--skip-health-checks flag used)
    call :log_info "Health checks skipped by user"
)

echo.
echo ===============================================================
echo STARTING MONTHLY BACKUP PROCESS
echo ===============================================================
echo Configuration: !TABLE_CONFIG_PATH!
echo Features: Error Recovery, Health Monitoring, Input Validation
echo Target: !MONTH_NAME! !YEAR!
echo ===============================================================
echo.

REM Validate backup script exists
call :validate_file_path "!MONTHLY_BACKUP_SCRIPT!" "Monthly backup script"
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Monthly backup script not found: !MONTHLY_BACKUP_SCRIPT!"
    exit /b %ERROR_FILE_NOT_FOUND%
)

REM Build parameters with proper validation
set "backup_params=--month "!MONTH_NAME!" --year !YEAR! --config-path "!TABLE_CONFIG_PATH!""
if "!DRY_RUN!"=="true" set "backup_params=!backup_params! --dry-run"
if "!VERBOSE!"=="true" set "backup_params=!backup_params! --verbose"
if "!FORCE_RECOVERY!"=="true" set "backup_params=!backup_params! --force-recovery"

call :log_info "Starting monthly backup processor..."
call :log_info "Running: !PYTHON_CMD! !MONTHLY_BACKUP_SCRIPT! !backup_params!"
echo.

REM Execute backup with proper error handling
call :safe_execute_with_retry "!PYTHON_CMD! !MONTHLY_BACKUP_SCRIPT! !backup_params!" "Monthly backup execution" 3
set "BACKUP_EXIT_CODE=!ERRORLEVEL!"

echo.
echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Exit code: !BACKUP_EXIT_CODE!
echo Completion time: %date% %time%
echo Log file: !LOG_FILE!
echo ===============================================================

REM Generate completion summary
call :log_operation_end "Monthly Backup System" !BACKUP_EXIT_CODE!

if !BACKUP_EXIT_CODE! EQU 0 (
    echo ✅ Monthly backup completed successfully!
    call :log_info "Monthly backup completed successfully"
) else (
    echo ❌ Monthly backup failed or completed with errors.
    echo Check the log file for details: !LOG_FILE!
    call :log_error "Monthly backup failed - check logs for details"

    REM Generate error report
    call :generate_error_report
)

echo.
echo Recent log entries:
echo ===============================================================
call :show_recent_logs 10
echo ===============================================================

exit /b !BACKUP_EXIT_CODE!

:show_monthly_help
echo ===============================================================
echo TNGD Monthly Backup System - Fixed Version
echo ===============================================================
echo.
echo FIXES IMPLEMENTED:
echo   ✅ Consistent delayed expansion usage
echo   ✅ Proper input validation and security hardening
echo   ✅ Standardized error handling with retry mechanisms
echo   ✅ Multiple configuration file fallbacks
echo   ✅ Comprehensive system health monitoring
echo   ✅ Centralized logging and configuration management
echo.
echo Usage:
echo   run_monthly_backup_enhanced.bat [month] [year] [options]
echo.
echo Parameters:
echo   month                  Month name (required, e.g., march, april)
echo   year                   Year (optional, default: 2025)
echo.
echo Options:
echo   --dry-run              Validate only, no backup
echo   --verbose              Enable verbose logging
echo   --force-recovery       Force recovery mode for failed operations
echo   --skip-health-checks   Skip system health checks (not recommended)
echo.
echo Examples:
echo   run_monthly_backup_enhanced.bat march 2025
echo   run_monthly_backup_enhanced.bat march 2025 --dry-run
echo   run_monthly_backup_enhanced.bat march 2025 --verbose --force-recovery
echo.
echo Configuration Discovery Order:
echo   1. tabletest\tables.json (primary)
echo   2. config\tables.json (fallback)
echo   3. backup\tables.json (secondary fallback)
echo   4. config\backup_tables.json (tertiary fallback)
echo.
echo Security Features:
echo   • Input validation for all parameters
echo   • Safe string validation to prevent injection
echo   • Proper error handling with detailed logging
echo   • Comprehensive health checks before execution
echo.
echo ===============================================================
exit /b 0
