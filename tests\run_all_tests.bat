@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Comprehensive Test Suite Runner                 S4NG-7
REM ===============================================================
REM Master test runner for all TNGD testing frameworks
REM - Unit tests
REM - Integration tests  
REM - Performance benchmarks
REM - System validation
REM - Comprehensive reporting
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Test suite variables
set "TOTAL_TESTS=0"
set "TOTAL_PASSED=0"
set "TOTAL_FAILED=0"
set "SUITE_RESULTS_FILE="
set "GENERATE_REPORT=true"

REM ===============================================================
REM MAIN TEST EXECUTION
REM ===============================================================

:main_test_suite
call :init_comprehensive_testing

echo ===============================================================
echo TNGD COMPREHENSIVE TEST SUITE
echo ===============================================================
echo.

REM Process command line arguments
set "TEST_SCOPE=all"
set "QUICK_MODE=false"
set "VERBOSE_MODE=false"

:parse_test_args
if "%~1"=="" goto test_args_done

if /i "%~1"=="--scope" (
    if not "%~2"=="" (
        set "TEST_SCOPE=%~2"
        shift & shift & goto parse_test_args
    )
)

if /i "%~1"=="--quick" (
    set "QUICK_MODE=true"
    shift & goto parse_test_args
)

if /i "%~1"=="--verbose" (
    set "VERBOSE_MODE=true"
    shift & goto parse_test_args
)

if /i "%~1"=="--no-report" (
    set "GENERATE_REPORT=false"
    shift & goto parse_test_args
)

if /i "%~1"=="--help" goto show_comprehensive_help

shift & goto parse_test_args

:test_args_done

echo Running comprehensive test suite: %TEST_SCOPE%
if "%QUICK_MODE%"=="true" echo Quick mode enabled
if "%VERBOSE_MODE%"=="true" echo Verbose mode enabled
echo.

REM Execute test suites based on scope
if /i "%TEST_SCOPE%"=="all" call :run_all_test_suites
if /i "%TEST_SCOPE%"=="unit" call :run_unit_tests
if /i "%TEST_SCOPE%"=="integration" call :run_integration_tests
if /i "%TEST_SCOPE%"=="performance" call :run_performance_tests
if /i "%TEST_SCOPE%"=="validation" call :run_system_validation

REM Generate comprehensive report
if "%GENERATE_REPORT%"=="true" call :generate_comprehensive_report

REM Show final results
call :show_final_results

exit /b %overall_result%

REM ===============================================================
REM TEST SUITE INITIALIZATION
REM ===============================================================

:init_comprehensive_testing
call :get_timestamp timestamp
set "TEST_RESULTS_DIR=tests\logs\comprehensive_%timestamp%"
if not exist "%TEST_RESULTS_DIR%" mkdir "%TEST_RESULTS_DIR%" 2>nul

set "SUITE_RESULTS_FILE=%TEST_RESULTS_DIR%\comprehensive_results.txt"

echo =============================================================== > "%SUITE_RESULTS_FILE%"
echo TNGD Comprehensive Test Suite Results >> "%SUITE_RESULTS_FILE%"
echo Started: %date% %time% >> "%SUITE_RESULTS_FILE%"
echo =============================================================== >> "%SUITE_RESULTS_FILE%"
echo. >> "%SUITE_RESULTS_FILE%"

goto :eof

REM ===============================================================
REM TEST SUITE EXECUTION
REM ===============================================================

:run_all_test_suites
echo Running all test suites...
echo.

call :run_unit_tests
call :run_integration_tests
call :run_performance_tests
call :run_system_validation

goto :eof

:run_unit_tests
echo.
echo ===============================================================
echo RUNNING UNIT TESTS
echo ===============================================================

set "unit_args="
if "%VERBOSE_MODE%"=="true" set "unit_args=--verbose"
if "%GENERATE_REPORT%"=="false" set "unit_args=%unit_args% --no-report"

call tests\run_unit_tests.bat %unit_args%
set "unit_exit_code=!ERRORLEVEL!"

call :record_suite_result "Unit Tests" %unit_exit_code%

goto :eof

:run_integration_tests
echo.
echo ===============================================================
echo RUNNING INTEGRATION TESTS
echo ===============================================================

set "integration_args="
if "%QUICK_MODE%"=="true" set "integration_args=--type workflow"

call tests\integration\integration_tests.bat %integration_args%
set "integration_exit_code=!ERRORLEVEL!"

call :record_suite_result "Integration Tests" %integration_exit_code%

goto :eof

:run_performance_tests
echo.
echo ===============================================================
echo RUNNING PERFORMANCE BENCHMARKS
echo ===============================================================

set "perf_args="
if "%QUICK_MODE%"=="true" set "perf_args=--type startup --iterations 3"
if "%GENERATE_REPORT%"=="false" set "perf_args=%perf_args% --no-report"

call tests\performance\performance_benchmarks.bat %perf_args%
set "performance_exit_code=!ERRORLEVEL!"

call :record_suite_result "Performance Tests" %performance_exit_code%

goto :eof

:run_system_validation
echo.
echo ===============================================================
echo RUNNING SYSTEM VALIDATION
echo ===============================================================

REM Test new system functionality
echo Testing new system functionality...
call bin\run_daily_backup_new.bat test dry-run >nul 2>&1
set "validation_exit_code=!ERRORLEVEL!"

if %validation_exit_code% EQU 0 (
    echo ✅ System validation PASSED
) else (
    echo ❌ System validation FAILED
)

call :record_suite_result "System Validation" %validation_exit_code%

REM Test monthly backup system
echo Testing monthly backup system...
call bin\run_monthly_backup_enhanced.bat help >nul 2>&1
set "monthly_exit_code=!ERRORLEVEL!"

if %monthly_exit_code% EQU 0 (
    echo ✅ Monthly backup validation PASSED
) else (
    echo ❌ Monthly backup validation FAILED
)

call :record_suite_result "Monthly Backup Validation" %monthly_exit_code%

goto :eof

REM ===============================================================
REM RESULT TRACKING
REM ===============================================================

:record_suite_result
setlocal
set "suite_name=%~1"
set "exit_code=%~2"

set /a "TOTAL_TESTS+=1"

if "%exit_code%"=="0" (
    set /a "TOTAL_PASSED+=1"
    echo %suite_name%: PASSED >> "%SUITE_RESULTS_FILE%"
) else (
    set /a "TOTAL_FAILED+=1"
    echo %suite_name%: FAILED (Exit Code: %exit_code%) >> "%SUITE_RESULTS_FILE%"
)

goto :eof

:show_final_results
echo.
echo ===============================================================
echo COMPREHENSIVE TEST SUITE RESULTS
echo ===============================================================
echo Total Test Suites: %TOTAL_TESTS%
echo Passed: %TOTAL_PASSED%
echo Failed: %TOTAL_FAILED%
echo.

if %TOTAL_FAILED% EQU 0 (
    echo ✅ ALL TEST SUITES PASSED!
    set "overall_result=0"
) else (
    echo ❌ %TOTAL_FAILED% TEST SUITES FAILED
    set "overall_result=1"
)

echo.
echo Results saved to: %SUITE_RESULTS_FILE%
echo ===============================================================

goto :eof

REM ===============================================================
REM COMPREHENSIVE REPORTING
REM ===============================================================

:generate_comprehensive_report
echo.
echo Generating comprehensive test report...

set "report_file=%TEST_RESULTS_DIR%\comprehensive_report.html"

echo ^<!DOCTYPE html^> > "%report_file%"
echo ^<html^> >> "%report_file%"
echo ^<head^> >> "%report_file%"
echo ^<title^>TNGD Comprehensive Test Report^</title^> >> "%report_file%"
echo ^<style^> >> "%report_file%"
echo body { font-family: Arial, sans-serif; margin: 20px; } >> "%report_file%"
echo .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; } >> "%report_file%"
echo .suite { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; } >> "%report_file%"
echo .pass { color: green; font-weight: bold; } >> "%report_file%"
echo .fail { color: red; font-weight: bold; } >> "%report_file%"
echo .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; } >> "%report_file%"
echo ^</style^> >> "%report_file%"
echo ^</head^> >> "%report_file%"
echo ^<body^> >> "%report_file%"

echo ^<div class="header"^> >> "%report_file%"
echo ^<h1^>TNGD Comprehensive Test Report^</h1^> >> "%report_file%"
echo ^<p^>Generated: %date% %time%^</p^> >> "%report_file%"
echo ^<p^>Test Scope: %TEST_SCOPE%^</p^> >> "%report_file%"
if "%QUICK_MODE%"=="true" echo ^<p^>Mode: Quick^</p^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

echo ^<div class="summary"^> >> "%report_file%"
echo ^<h2^>Executive Summary^</h2^> >> "%report_file%"
echo ^<p^>Total Test Suites: %TOTAL_TESTS%^</p^> >> "%report_file%"
echo ^<p class="pass"^>Passed: %TOTAL_PASSED%^</p^> >> "%report_file%"
echo ^<p class="fail"^>Failed: %TOTAL_FAILED%^</p^> >> "%report_file%"

if %TOTAL_FAILED% EQU 0 (
    echo ^<p class="pass"^>^<strong^>Overall Result: ALL TESTS PASSED^</strong^>^</p^> >> "%report_file%"
) else (
    echo ^<p class="fail"^>^<strong^>Overall Result: %TOTAL_FAILED% SUITES FAILED^</strong^>^</p^> >> "%report_file%"
)

echo ^</div^> >> "%report_file%"

echo ^<h2^>Test Suite Details^</h2^> >> "%report_file%"
echo ^<div class="suite"^> >> "%report_file%"
echo ^<h3^>Test Results^</h3^> >> "%report_file%"
echo ^<pre^> >> "%report_file%"

REM Include test results
if exist "%SUITE_RESULTS_FILE%" (
    type "%SUITE_RESULTS_FILE%" >> "%report_file%"
)

echo ^</pre^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

REM Add links to individual reports
echo ^<h2^>Individual Test Reports^</h2^> >> "%report_file%"
echo ^<ul^> >> "%report_file%"

REM Check for individual test reports
if exist "tests\logs\unit_*\test_report.html" (
    echo ^<li^>^<a href="../unit_*/test_report.html"^>Unit Test Report^</a^>^</li^> >> "%report_file%"
)

if exist "tests\logs\performance_*\performance_report.html" (
    echo ^<li^>^<a href="../performance_*/performance_report.html"^>Performance Report^</a^>^</li^> >> "%report_file%"
)

echo ^</ul^> >> "%report_file%"

echo ^<h2^>System Information^</h2^> >> "%report_file%"
echo ^<div class="suite"^> >> "%report_file%"
echo ^<p^>Operating System: %OS%^</p^> >> "%report_file%"
echo ^<p^>Computer: %COMPUTERNAME%^</p^> >> "%report_file%"
echo ^<p^>User: %USERNAME%^</p^> >> "%report_file%"
echo ^<p^>Working Directory: %CD%^</p^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

echo ^</body^> >> "%report_file%"
echo ^</html^> >> "%report_file%"

echo ✅ Comprehensive report generated: %report_file%

goto :eof

REM ===============================================================
REM UTILITIES
REM ===============================================================

:get_timestamp
REM Get timestamp in YYYY-MM-DD_HH-MM-SS format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "%dt%"=="" (
    set "timestamp=%date:~-4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
) else (
    set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"
)
set "%~1=%timestamp%"
goto :eof

:show_comprehensive_help
echo ===============================================================
echo TNGD Comprehensive Test Suite Runner - Help
echo ===============================================================
echo.
echo Usage:
echo   run_all_tests.bat [options]
echo.
echo Options:
echo   --scope SCOPE         Test scope (default: all)
echo   --quick               Quick mode - reduced test coverage
echo   --verbose             Enable verbose output
echo   --no-report           Skip HTML report generation
echo   --help                Show this help message
echo.
echo Available Test Scopes:
echo   all                   Run all test suites
echo   unit                  Run unit tests only
echo   integration           Run integration tests only
echo   performance           Run performance benchmarks only
echo   validation            Run system validation only
echo.
echo Examples:
echo   run_all_tests.bat
echo   run_all_tests.bat --scope unit --verbose
echo   run_all_tests.bat --quick
echo   run_all_tests.bat --scope performance --no-report
echo.
echo Output:
echo   Test results are saved to: tests\logs\comprehensive_TIMESTAMP\
echo   HTML report is generated (unless --no-report is used)
echo   Individual test reports are linked in the main report
echo.
echo Exit Codes:
echo   0 = All test suites passed
echo   1 = One or more test suites failed
echo.
echo Test Suites Included:
echo   • Unit Tests: Test individual components and functions
echo   • Integration Tests: Test module interactions and workflows
echo   • Performance Tests: Benchmark system performance
echo   • System Validation: Validate overall system functionality
echo.
echo ===============================================================

exit /b 0
