@echo off
REM ===============================================================
REM TNGD Common Functions Library                         S4NG-7
REM ===============================================================
REM Shared utility functions for TNGD backup system
REM - Parameter validation
REM - Date/time utilities
REM - Directory management
REM - System checks
REM ===============================================================

REM Initialize common variables if not already set
if not defined TNGD_ROOT set TNGD_ROOT=%~dp0..\..
if not defined PYTHON_CMD set PYTHON_CMD=python

REM ===============================================================
REM PARAMETER VALIDATION FUNCTIONS
REM ===============================================================

:validate_required_param
REM Usage: call :validate_required_param "param_value" "param_name"
setlocal
set "param_value=%~1"
set "param_name=%~2"

if "%param_value%"=="" (
    call :log_error "Missing required parameter: %param_name%"
    exit /b 1
)
goto :eof

:validate_safe_string
REM Usage: call :validate_safe_string "input_string" "param_name"
REM Validates that string contains only safe characters
setlocal
set "input_string=%~1"
set "param_name=%~2"

if "%input_string%"=="" (
    call :log_error "Empty parameter: %param_name%"
    exit /b 1
)

REM Check for dangerous characters
echo "%input_string%" | findstr /r "[&|<>^\"*?]" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    call :log_error "Invalid characters in parameter: %param_name%"
    exit /b 1
)

goto :eof

:validate_file_path
REM Usage: call :validate_file_path "file_path" "description"
setlocal
set "file_path=%~1"
set "description=%~2"

call :validate_required_param "%file_path%" "%description%"
if %ERRORLEVEL% NEQ 0 exit /b 1

if not exist "%file_path%" (
    call :log_error "File not found: %description% (%file_path%)"
    exit /b 1
)

goto :eof

:validate_directory_path
REM Usage: call :validate_directory_path "dir_path" "description"
setlocal
set "dir_path=%~1"
set "description=%~2"

call :validate_required_param "%dir_path%" "%description%"
if %ERRORLEVEL% NEQ 0 exit /b 1

if not exist "%dir_path%" (
    call :log_error "Directory not found: %description% (%dir_path%)"
    exit /b 1
)

goto :eof

REM ===============================================================
REM DATE/TIME UTILITIES
REM ===============================================================

:get_timestamp
REM Usage: call :get_timestamp timestamp_var
REM Returns: YYYY-MM-DD_HH-MM-SS format
setlocal enabledelayedexpansion
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "!dt!"=="" (
    REM Fallback to date/time commands
    for /f "tokens=1-3 delims=/" %%a in ("%date%") do (
        set "mm=%%a"
        set "dd=%%b" 
        set "yyyy=%%c"
    )
    for /f "tokens=1-3 delims=:" %%a in ("%time%") do (
        set "hh=%%a"
        set "min=%%b"
        set "sec=%%c"
    )
    set "timestamp=!yyyy!-!mm!-!dd!_!hh!-!min!-!sec:~0,2!"
) else (
    set "timestamp=!dt:~0,4!-!dt:~4,2!-!dt:~6,2!_!dt:~8,2!-!dt:~10,2!-!dt:~12,2!"
)
endlocal & set "%~1=%timestamp%"
goto :eof

:get_date_components
REM Usage: call :get_date_components yyyy_var mm_var dd_var
setlocal enabledelayedexpansion
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "!dt!"=="" (
    REM Fallback method
    for /f "tokens=1-3 delims=/" %%a in ("%date%") do (
        set "mm=%%a"
        set "dd=%%b"
        set "yyyy=%%c"
    )
) else (
    set "yyyy=!dt:~0,4!"
    set "mm=!dt:~4,2!"
    set "dd=!dt:~6,2!"
)
endlocal & set "%~1=%yyyy%" & set "%~2=%mm%" & set "%~3=%dd%"
goto :eof

REM ===============================================================
REM DIRECTORY MANAGEMENT
REM ===============================================================

:ensure_directory
REM Usage: call :ensure_directory "directory_path"
setlocal
set "dir_path=%~1"

if not exist "%dir_path%" (
    mkdir "%dir_path%" 2>nul
    if !ERRORLEVEL! NEQ 0 (
        call :log_error "Failed to create directory: %dir_path%"
        exit /b 1
    )
    call :log_info "Created directory: %dir_path%"
)
goto :eof

:create_log_directory
REM Usage: call :create_log_directory log_type date_str log_dir_var
REM Creates organized log directory structure
setlocal enabledelayedexpansion
set "log_type=%~1"
set "date_str=%~2"

if "%date_str%"=="" call :get_timestamp date_str

set "log_dir=%TNGD_ROOT%\logs\%log_type%\%date_str:~0,10%"
call :ensure_directory "%log_dir%"
if !ERRORLEVEL! NEQ 0 exit /b 1

endlocal & set "%~3=%log_dir%"
goto :eof

REM ===============================================================
REM SYSTEM CHECKS
REM ===============================================================

:check_python_available
REM Usage: call :check_python_available
%PYTHON_CMD% --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    call :log_error "Python not available or not in PATH"
    exit /b 1
)
goto :eof

:check_disk_space
REM Usage: call :check_disk_space min_gb_required
setlocal
set "min_gb=%~1"
if "%min_gb%"=="" set "min_gb=5"

%PYTHON_CMD% -c "import shutil; free = shutil.disk_usage('.').free / (1024**3); print(f'Free space: {free:.1f} GB'); exit(0 if free > %min_gb% else 1)" 2>nul
if %ERRORLEVEL% NEQ 0 (
    call :log_error "Insufficient disk space (less than %min_gb%GB free)"
    exit /b 1
)
goto :eof

:check_admin_rights
REM Usage: call :check_admin_rights
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    call :log_error "Administrator rights required for this operation"
    exit /b 1
)
goto :eof

REM ===============================================================
REM UTILITY FUNCTIONS
REM ===============================================================

:count_json_tables
REM Usage: call :count_json_tables "json_file_path" count_var
setlocal enabledelayedexpansion
set "json_file=%~1"
set "table_count=0"

if exist "%json_file%" (
    for /f "tokens=1 delims=:" %%a in ('findstr /n /c:"]" "%json_file%" 2^>nul') do set "total_lines=%%a"
    for /f "tokens=1 delims=:" %%a in ('findstr /n /c:"[" "%json_file%" 2^>nul') do set "start_line=%%a"
    if defined total_lines if defined start_line (
        set /a "table_count=!total_lines!-!start_line!-1"
        if !table_count! LSS 0 set "table_count=0"
    )
)

endlocal & set "%~2=%table_count%"
goto :eof

:cleanup_temp_files
REM Usage: call :cleanup_temp_files "pattern"
setlocal
set "pattern=%~1"
if "%pattern%"=="" set "pattern=*.tmp"

call :log_info "Cleaning up temporary files: %pattern%"
del /q "%pattern%" 2>nul
goto :eof

REM ===============================================================
REM LOGGING FUNCTIONS (Basic - will be enhanced in logging.bat)
REM ===============================================================

:log_info
echo [INFO] [%date% %time%] %~1
if defined LOG_FILE echo [INFO] [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof

:log_error
echo [ERROR] [%date% %time%] %~1
if defined LOG_FILE echo [ERROR] [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof

:log_warning
echo [WARNING] [%date% %time%] %~1
if defined LOG_FILE echo [WARNING] [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof
