@echo off
REM ===============================================================
REM TNGD Logging Library                                 S4NG-7
REM ===============================================================
REM Centralized logging system for TNGD backup system
REM - Multiple log levels (DEBUG, INFO, WARNING, ERROR)
REM - Structured log formatting
REM - Log rotation and cleanup
REM - Performance logging
REM ===============================================================

REM Load common functions if not already loaded
if not defined TNGD_COMMON_LOADED call "%~dp0common_functions.bat"

REM ===============================================================
REM LOGGING CONFIGURATION
REM ===============================================================

REM Log levels (numeric for comparison)
set LOG_LEVEL_DEBUG=10
set LOG_LEVEL_INFO=20
set LOG_LEVEL_WARNING=30
set LOG_LEVEL_ERROR=40

REM Default log level (can be overridden)
if not defined CURRENT_LOG_LEVEL set CURRENT_LOG_LEVEL=%LOG_LEVEL_INFO%

REM Log format settings
set LOG_FORMAT_TIMESTAMP=1
set LOG_FORMAT_LEVEL=1
set LOG_FORMAT_COMPONENT=1

REM ===============================================================
REM CORE LOGGING FUNCTIONS
REM ===============================================================

:init_logging
REM Usage: call :init_logging "log_file_path" "component_name" [log_level]
setlocal enabledelayedexpansion
set "log_file=%~1"
set "component_name=%~2"
set "log_level=%~3"

REM Set global logging variables
endlocal & (
    set "LOG_FILE=%log_file%"
    set "LOG_COMPONENT=%component_name%"
    if not "%log_level%"=="" set "CURRENT_LOG_LEVEL=%log_level%"
)

REM Ensure log directory exists
for %%F in ("%LOG_FILE%") do (
    if not exist "%%~dpF" mkdir "%%~dpF" 2>nul
)

REM Initialize log file with header
call :log_header
goto :eof

:log_header
if not exist "%LOG_FILE%" (
    echo =============================================================== > "%LOG_FILE%"
    echo TNGD Backup System Log - %LOG_COMPONENT% >> "%LOG_FILE%"
    echo Started: %date% %time% >> "%LOG_FILE%"
    echo =============================================================== >> "%LOG_FILE%"
)
goto :eof

:log_debug
REM Usage: call :log_debug "message"
if %CURRENT_LOG_LEVEL% LEQ %LOG_LEVEL_DEBUG% (
    call :write_log "DEBUG" "%~1"
)
goto :eof

:log_info
REM Usage: call :log_info "message"
if %CURRENT_LOG_LEVEL% LEQ %LOG_LEVEL_INFO% (
    call :write_log "INFO" "%~1"
    echo [INFO] %~1
)
goto :eof

:log_warning
REM Usage: call :log_warning "message"
if %CURRENT_LOG_LEVEL% LEQ %LOG_LEVEL_WARNING% (
    call :write_log "WARNING" "%~1"
    echo [WARNING] %~1
)
goto :eof

:log_error
REM Usage: call :log_error "message"
if %CURRENT_LOG_LEVEL% LEQ %LOG_LEVEL_ERROR% (
    call :write_log "ERROR" "%~1"
    echo [ERROR] %~1
)
goto :eof

:write_log
REM Usage: call :write_log "level" "message"
setlocal enabledelayedexpansion
set "level=%~1"
set "message=%~2"

REM Build log entry
set "log_entry="

REM Add timestamp
if "%LOG_FORMAT_TIMESTAMP%"=="1" (
    set "log_entry=!log_entry![%date% %time%] "
)

REM Add log level
if "%LOG_FORMAT_LEVEL%"=="1" (
    set "log_entry=!log_entry![%level%] "
)

REM Add component name
if "%LOG_FORMAT_COMPONENT%"=="1" (
    if defined LOG_COMPONENT (
        set "log_entry=!log_entry![%LOG_COMPONENT%] "
    )
)

REM Add message
set "log_entry=!log_entry!%message%"

REM Write to log file
if defined LOG_FILE (
    echo !log_entry! >> "%LOG_FILE%"
)

goto :eof

REM ===============================================================
REM SPECIALIZED LOGGING FUNCTIONS
REM ===============================================================

:log_operation_start
REM Usage: call :log_operation_start "operation_name"
setlocal
set "operation=%~1"
call :log_info "=== STARTING: %operation% ==="
call :log_info "Start time: %date% %time%"
goto :eof

:log_operation_end
REM Usage: call :log_operation_end "operation_name" exit_code
setlocal
set "operation=%~1"
set "exit_code=%~2"

call :log_info "=== COMPLETED: %operation% ==="
call :log_info "End time: %date% %time%"
call :log_info "Exit code: %exit_code%"

if "%exit_code%"=="0" (
    call :log_info "Status: SUCCESS"
) else (
    call :log_error "Status: FAILED (Exit code: %exit_code%)"
)
goto :eof

:log_progress
REM Usage: call :log_progress current_item total_items "description"
setlocal
set "current=%~1"
set "total=%~2"
set "description=%~3"

set /a "percent=(%current%*100)/%total%"
call :log_info "Progress: %current%/%total% (%percent%%) - %description%"
goto :eof

:log_performance
REM Usage: call :log_performance "operation" start_time end_time
setlocal enabledelayedexpansion
set "operation=%~1"
set "start_time=%~2"
set "end_time=%~3"

REM Calculate duration (simplified - for more accurate timing, use external tools)
call :log_info "Performance: %operation% completed"
call :log_info "Start: %start_time%"
call :log_info "End: %end_time%"
goto :eof

:log_system_info
REM Usage: call :log_system_info
call :log_info "=== SYSTEM INFORMATION ==="
call :log_info "OS: %OS%"
call :log_info "Computer: %COMPUTERNAME%"
call :log_info "User: %USERNAME%"
call :log_info "Working Directory: %CD%"
call :log_info "Python Command: %PYTHON_CMD%"

REM Check Python version
for /f "tokens=*" %%a in ('%PYTHON_CMD% --version 2^>^&1') do (
    call :log_info "Python Version: %%a"
)

REM Check disk space
for /f "tokens=*" %%a in ('%PYTHON_CMD% -c "import shutil; free = shutil.disk_usage('.').free / (1024**3); print(f'Free disk space: {free:.1f} GB')" 2^>nul') do (
    call :log_info "%%a"
)

call :log_info "=========================="
goto :eof

REM ===============================================================
REM LOG MANAGEMENT FUNCTIONS
REM ===============================================================

:rotate_logs
REM Usage: call :rotate_logs "log_directory" max_files
setlocal enabledelayedexpansion
set "log_dir=%~1"
set "max_files=%~2"

if "%max_files%"=="" set "max_files=10"

call :log_info "Rotating logs in: %log_dir%"

if not exist "%log_dir%" (
    call :log_warning "Log directory does not exist: %log_dir%"
    goto :eof
)

REM Count log files
set "file_count=0"
for %%f in ("%log_dir%\*.log") do (
    set /a "file_count+=1"
)

call :log_info "Found %file_count% log files"

if %file_count% LEQ %max_files% (
    call :log_info "No log rotation needed"
    goto :eof
)

REM Delete oldest files
set /a "files_to_delete=%file_count%-%max_files%"
call :log_info "Deleting %files_to_delete% oldest log files"

REM Get oldest files and delete them
for /f "skip=%max_files%" %%f in ('dir "%log_dir%\*.log" /b /o:d') do (
    del "%log_dir%\%%f" 2>nul
    if !ERRORLEVEL! EQU 0 (
        call :log_info "Deleted old log file: %%f"
    ) else (
        call :log_warning "Failed to delete log file: %%f"
    )
)

goto :eof

:cleanup_old_logs
REM Usage: call :cleanup_old_logs "log_directory" days_to_keep
setlocal
set "log_dir=%~1"
set "days_to_keep=%~2"

if "%days_to_keep%"=="" set "days_to_keep=30"

call :log_info "Cleaning up logs older than %days_to_keep% days in: %log_dir%"

if not exist "%log_dir%" (
    call :log_warning "Log directory does not exist: %log_dir%"
    goto :eof
)

REM Use forfiles to delete old files
forfiles /p "%log_dir%" /s /m *.log /d -%days_to_keep% /c "cmd /c del @path" 2>nul
if %ERRORLEVEL% EQU 0 (
    call :log_info "Old log cleanup completed"
) else (
    call :log_info "No old logs found to clean up"
)

goto :eof

:archive_logs
REM Usage: call :archive_logs "log_directory" "archive_directory"
setlocal enabledelayedexpansion
set "log_dir=%~1"
set "archive_dir=%~2"

call :get_timestamp timestamp
set "archive_name=logs_archive_%timestamp%.zip"

call :log_info "Archiving logs from %log_dir% to %archive_dir%\%archive_name%"

REM Ensure archive directory exists
if not exist "%archive_dir%" mkdir "%archive_dir%" 2>nul

REM Create archive using PowerShell (available on Windows 10+)
powershell -Command "Compress-Archive -Path '%log_dir%\*.log' -DestinationPath '%archive_dir%\%archive_name%' -Force" 2>nul
if !ERRORLEVEL! EQU 0 (
    call :log_info "Log archive created successfully: %archive_name%"
    
    REM Delete original log files after successful archive
    del "%log_dir%\*.log" 2>nul
    call :log_info "Original log files cleaned up"
) else (
    call :log_error "Failed to create log archive"
)

goto :eof

REM ===============================================================
REM LOG ANALYSIS FUNCTIONS
REM ===============================================================

:show_recent_logs
REM Usage: call :show_recent_logs [number_of_lines]
setlocal
set "lines=%~1"
if "%lines%"=="" set "lines=20"

if not exist "%LOG_FILE%" (
    echo No log file found: %LOG_FILE%
    goto :eof
)

echo ===============================================================
echo RECENT LOG ENTRIES (Last %lines% lines):
echo ===============================================================
powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last %lines%"
echo ===============================================================
goto :eof

:search_logs
REM Usage: call :search_logs "search_term" [context_lines]
setlocal
set "search_term=%~1"
set "context_lines=%~2"

if "%search_term%"=="" (
    call :log_error "Search term required"
    goto :eof
)

if "%context_lines%"=="" set "context_lines=2"

if not exist "%LOG_FILE%" (
    echo No log file found: %LOG_FILE%
    goto :eof
)

echo ===============================================================
echo SEARCH RESULTS for: %search_term%
echo ===============================================================
findstr /i /c:"%search_term%" "%LOG_FILE%"
echo ===============================================================
goto :eof
