# TNGD Backup System - User Guide

## Overview

The TNGD Backup System is a comprehensive, modular backup solution designed for reliability, security, and ease of use. This guide covers all aspects of using the system effectively.

## Table of Contents

1. [Quick Start](#quick-start)
2. [System Architecture](#system-architecture)
3. [Installation and Setup](#installation-and-setup)
4. [Daily Operations](#daily-operations)
5. [Monthly Operations](#monthly-operations)
6. [Testing and Validation](#testing-and-validation)
7. [Troubleshooting](#troubleshooting)
8. [Advanced Configuration](#advanced-configuration)

## Quick Start

### 1. Test the System
```batch
# Validate system configuration
bin\run_daily_backup_new.bat test dry-run

# Test single table functionality
bin\run_daily_backup_new.bat test single-table
```

### 2. Run Your First Backup
```batch
# Daily backup (dry run)
bin\run_daily_backup_new.bat --dry-run

# Actual daily backup
bin\run_daily_backup_new.bat
```

### 3. Setup Automation
```batch
# Create scheduled tasks (requires admin rights)
bin\run_daily_backup_new.bat setup create-all
```

## System Architecture

### Modular Design
The system is built with a modular architecture:

```
bin/
├── run_daily_backup_new.bat          # Main entry point
├── run_monthly_backup_enhanced.bat   # Monthly backup (fixed)
├── shared/                           # Shared libraries
│   ├── common_functions.bat          # Core utilities
│   ├── error_handling.bat            # Error management
│   ├── logging.bat                   # Logging system
│   └── config_manager.bat            # Configuration
├── backup/                           # Backup modules
│   ├── daily_backup.bat              # Daily operations
│   └── single_table_backup.bat       # Single table ops
├── test/                             # Testing framework
│   └── test_runner.bat               # Test execution
└── setup/                            # Scheduler setup
    └── scheduler_setup.bat           # Task management
```

### Key Features
- ✅ **Modular Architecture**: Focused, maintainable components
- ✅ **Security Hardening**: Input validation and injection prevention
- ✅ **Error Recovery**: Automatic retry with exponential backoff
- ✅ **Comprehensive Logging**: Multi-level logging with rotation
- ✅ **Configuration Management**: Centralized with fallbacks
- ✅ **Testing Framework**: Unit, integration, and performance tests

## Installation and Setup

### Prerequisites
- Windows operating system
- Python 3.x installed and in PATH
- Administrative rights (for scheduled task creation)
- Network access to database and OSS storage

### Initial Setup
1. **Verify Python Installation**
   ```batch
   python --version
   ```

2. **Test System Components**
   ```batch
   bin\run_daily_backup_new.bat test dry-run
   ```

3. **Configure Tables**
   - Ensure `tabletest\tables.json` exists with your table list
   - Or place configuration in `config\tables.json`

4. **Setup Automation** (Optional)
   ```batch
   # Run as Administrator
   bin\run_daily_backup_new.bat setup create-all
   ```

## Daily Operations

### Basic Daily Backup
```batch
# Standard daily backup
bin\run_daily_backup_new.bat

# With verbose logging
bin\run_daily_backup_new.bat --verbose

# Dry run (validation only)
bin\run_daily_backup_new.bat --dry-run
```

### Advanced Options
```batch
# Custom chunk size
bin\run_daily_backup_new.bat --chunk-size 50000

# Custom timeout
bin\run_daily_backup_new.bat --timeout 3600

# Single table mode for testing
bin\run_daily_backup_new.bat --single-table

# Force email notifications
bin\run_daily_backup_new.bat --force-email
```

### Single Table Operations
```batch
# List available tables
bin\run_daily_backup_new.bat single-table --list-tables

# Backup specific table
bin\run_daily_backup_new.bat single-table my.app.tngd.waf

# Backup with custom parameters
bin\run_daily_backup_new.bat single-table my.app.tngd.waf --days 7 --chunk-size 25000

# Test single table (dry run)
bin\run_daily_backup_new.bat single-table my.app.tngd.waf --dry-run
```

## Monthly Operations

### Basic Monthly Backup
```batch
# Monthly backup for March 2025
bin\run_monthly_backup_enhanced.bat march 2025

# With verbose logging
bin\run_monthly_backup_enhanced.bat march 2025 --verbose

# Dry run validation
bin\run_monthly_backup_enhanced.bat march 2025 --dry-run
```

### Advanced Monthly Options
```batch
# Force recovery mode
bin\run_monthly_backup_enhanced.bat march 2025 --force-recovery

# Skip health checks (not recommended)
bin\run_monthly_backup_enhanced.bat march 2025 --skip-health-checks

# Combined options
bin\run_monthly_backup_enhanced.bat march 2025 --verbose --force-recovery
```

## Testing and Validation

### Test Types
```batch
# Quick validation
bin\run_daily_backup_new.bat test dry-run

# Single table test
bin\run_daily_backup_new.bat test single-table

# Full system test (limited tables)
bin\run_daily_backup_new.bat test full-test 3

# Performance benchmarks
bin\run_daily_backup_new.bat test performance

# Error handling tests
bin\run_daily_backup_new.bat test error-handling

# Comprehensive test suite
bin\run_daily_backup_new.bat test all-tests

# Quick test suite
bin\run_daily_backup_new.bat test quick
```

### Running Tests Directly
```batch
# Unit tests
tests\run_unit_tests.bat

# Integration tests
tests\integration\integration_tests.bat

# Performance benchmarks
tests\performance\performance_benchmarks.bat
```

## Scheduler Management

### Creating Scheduled Tasks
```batch
# Create daily backup task
bin\run_daily_backup_new.bat setup create-daily --time 02:00

# Create weekly backup task
bin\run_daily_backup_new.bat setup create-weekly --time 03:00 --day SUN

# Create monthly backup task
bin\run_daily_backup_new.bat setup create-monthly --time 04:00 --day 1

# Create all tasks at once
bin\run_daily_backup_new.bat setup create-all
```

### Managing Scheduled Tasks
```batch
# List all TNGD backup tasks
bin\run_daily_backup_new.bat setup list

# Check task status
bin\run_daily_backup_new.bat setup status TNGD_DailyBackup

# Delete a task
bin\run_daily_backup_new.bat setup delete TNGD_DailyBackup
```

## Configuration

### Configuration Files
The system uses a fallback configuration discovery:

1. `tabletest\tables.json` (primary)
2. `config\tables.json` (fallback)
3. `backup\tables.json` (secondary fallback)
4. `config\backup_tables.json` (tertiary fallback)

### Environment Variables
You can override defaults by setting environment variables:
```batch
set CHUNK_SIZE=50000
set TIMEOUT=3600
set RETRY_COUNT=5
set PYTHON_CMD=python3
```

### Custom Configuration
Create `config\environment.config`:
```
# Custom configuration
CHUNK_SIZE=75000
TIMEOUT=2400
RETRY_COUNT=4
LOG_RETENTION_DAYS=45
MIN_DISK_SPACE_GB=10
```

## Logging and Monitoring

### Log Locations
- **Daily Backup**: `logs\daily\YYYY-MM-DD\`
- **Monthly Backup**: `logs\monthly\YYYY-MM-DD\`
- **Single Table**: `logs\single_table\YYYY-MM-DD\`
- **Tests**: `logs\tests\`
- **Setup**: `logs\setup\`

### Log Levels
- **DEBUG**: Detailed diagnostic information
- **INFO**: General operational messages
- **WARNING**: Warning conditions
- **ERROR**: Error conditions

### Viewing Logs
```batch
# Recent log entries are shown after each operation
# Full logs are available in the respective log directories
# HTML reports are generated for tests and performance benchmarks
```

## Exit Codes

| Code | Meaning |
|------|---------|
| 0 | Success |
| 1 | General error or warnings |
| 2 | Invalid parameters |
| 3 | File not found |
| 4 | Access denied |
| 5 | Disk full |
| 6 | Network error |
| 7 | Timeout |
| 8 | Configuration error |
| 9 | Python error |
| 10 | Backup failed |

## Best Practices

### Daily Operations
1. **Always test first**: Use `--dry-run` for validation
2. **Monitor logs**: Check log files for warnings or errors
3. **Verify disk space**: Ensure adequate space before large backups
4. **Use appropriate chunk sizes**: Balance performance vs. memory usage

### Monthly Operations
1. **Plan ahead**: Monthly backups can take significant time
2. **Verify configuration**: Ensure table configuration is current
3. **Monitor resources**: Watch disk space and network usage
4. **Test recovery**: Periodically test backup restoration

### Maintenance
1. **Log rotation**: Logs are automatically rotated and cleaned
2. **Configuration updates**: Keep table lists current
3. **Performance monitoring**: Use performance benchmarks regularly
4. **Security updates**: Keep Python and dependencies updated

## Migration from Old System

If you're migrating from the old monolithic system:

```batch
# Use the migration utility
bin\migrate_to_new_system.bat

# Follow the interactive prompts to:
# 1. Backup old system
# 2. Test new system
# 3. Migrate to new system
# 4. Rollback if needed
```

## Support and Troubleshooting

For troubleshooting common issues, see the [Troubleshooting Guide](TROUBLESHOOTING.md).

For additional support:
1. Check log files for detailed error information
2. Run diagnostic tests: `bin\run_daily_backup_new.bat test dry-run`
3. Verify configuration: `bin\run_daily_backup_new.bat test quick`
4. Review system requirements and prerequisites
