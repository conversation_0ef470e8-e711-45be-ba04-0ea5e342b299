# TNGD Backup System - Troubleshooting Guide

## Quick Diagnostic Commands

Before diving into specific issues, run these diagnostic commands:

```batch
# System validation
bin\run_daily_backup_new.bat test dry-run

# Quick system check
bin\run_daily_backup_new.bat test quick

# Check configuration
bin\run_daily_backup_new.bat help
```

## Common Issues and Solutions

### 1. Python Not Found

**Symptoms:**
- Error: "Python not available or not in PATH"
- Exit code: 9

**Solutions:**
```batch
# Check if Python is installed
python --version

# If not installed, install Python 3.x
# Add Python to PATH environment variable

# Alternative: Set custom Python command
set PYTHON_CMD=python3
# or
set PYTHON_CMD=C:\Python39\python.exe
```

### 2. Configuration File Not Found

**Symptoms:**
- Error: "No table configuration found in any fallback location"
- Exit code: 8

**Solutions:**
```batch
# Check if configuration exists in any of these locations:
dir tabletest\tables.json
dir config\tables.json
dir backup\tables.json

# Create a basic configuration file:
echo [ > tabletest\tables.json
echo   "your.table.name1", >> tabletest\tables.json
echo   "your.table.name2" >> tabletest\tables.json
echo ] >> tabletest\tables.json
```

### 3. Invalid Parameters

**Symptoms:**
- Error: "Invalid characters in parameter"
- Exit code: 2

**Solutions:**
```batch
# Avoid special characters in parameters
# Bad:  bin\run_daily_backup_new.bat single-table "table&name"
# Good: bin\run_daily_backup_new.bat single-table "table_name"

# Use quotes for parameters with spaces
bin\run_daily_backup_new.bat single-table "my table name"

# Check parameter format
bin\run_daily_backup_new.bat help
```

### 4. Insufficient Disk Space

**Symptoms:**
- Warning: "Low disk space detected"
- Error: "Not enough disk space available for backup"
- Exit code: 5

**Solutions:**
```batch
# Check available disk space
dir

# Run emergency cleanup
python utils\disk_cleanup.py --emergency --verbose

# Free up space manually and retry
bin\run_daily_backup_new.bat --dry-run
```

### 5. Access Denied (Scheduled Tasks)

**Symptoms:**
- Error: "Administrator rights required"
- Exit code: 4

**Solutions:**
```batch
# Run Command Prompt as Administrator
# Right-click Command Prompt → "Run as administrator"

# Then run setup commands:
bin\run_daily_backup_new.bat setup create-daily
```

### 6. Network/Database Connection Issues

**Symptoms:**
- Backup fails during execution
- Timeout errors
- Exit code: 6 or 7

**Solutions:**
```batch
# Test with dry run first
bin\run_daily_backup_new.bat --dry-run

# Increase timeout
bin\run_daily_backup_new.bat --timeout 3600

# Check network connectivity
ping your-database-server

# Test single table first
bin\run_daily_backup_new.bat single-table small_table_name --dry-run
```

### 7. Module Loading Errors

**Symptoms:**
- Error: "The system cannot find the path specified"
- Missing shared library errors

**Solutions:**
```batch
# Verify file structure
dir bin\shared\
dir bin\backup\
dir bin\test\
dir bin\setup\

# Check current directory
cd
# Should be in the TNGD root directory

# If files are missing, re-extract or re-install the system
```

### 8. Log File Issues

**Symptoms:**
- Cannot create log files
- Log directory errors

**Solutions:**
```batch
# Check if logs directory exists
dir logs\

# Create logs directory manually
mkdir logs
mkdir logs\daily
mkdir logs\monthly
mkdir logs\tests

# Check permissions
# Ensure write access to the logs directory
```

## Advanced Troubleshooting

### Debug Mode

Enable verbose logging for detailed diagnostics:
```batch
# Verbose daily backup
bin\run_daily_backup_new.bat --verbose

# Verbose monthly backup
bin\run_monthly_backup_enhanced.bat march 2025 --verbose

# Verbose testing
bin\run_daily_backup_new.bat test dry-run --verbose
```

### Log Analysis

Check log files for detailed error information:
```batch
# Recent daily backup logs
dir logs\daily\ /o-d

# View latest log file
type "logs\daily\YYYY-MM-DD\latest_log_file.log"

# Search for errors in logs
findstr /i "error" "logs\daily\YYYY-MM-DD\*.log"
findstr /i "fail" "logs\daily\YYYY-MM-DD\*.log"
```

### Configuration Validation

Validate your configuration:
```batch
# Test JSON validity
python -c "import json; json.load(open('tabletest/tables.json')); print('Valid JSON')"

# Check table count
python -c "import json; tables = json.load(open('tabletest/tables.json')); print(f'Found {len(tables)} tables')"
```

### Performance Issues

If backups are running slowly:
```batch
# Run performance benchmarks
tests\performance\performance_benchmarks.bat

# Use smaller chunk sizes
bin\run_daily_backup_new.bat --chunk-size 25000

# Test with single table first
bin\run_daily_backup_new.bat single-table small_table --verbose
```

## Error Code Reference

| Exit Code | Error Type | Common Causes | Solutions |
|-----------|------------|---------------|-----------|
| 0 | Success | - | No action needed |
| 1 | General/Warnings | Partial failures, warnings | Check logs for details |
| 2 | Invalid Parameters | Wrong command syntax | Check help: `--help` |
| 3 | File Not Found | Missing scripts/config | Verify file structure |
| 4 | Access Denied | Insufficient permissions | Run as Administrator |
| 5 | Disk Full | Low disk space | Free up space |
| 6 | Network Error | Connection issues | Check network/database |
| 7 | Timeout | Operation too slow | Increase timeout |
| 8 | Configuration Error | Invalid config files | Fix configuration |
| 9 | Python Error | Python not available | Install/configure Python |
| 10 | Backup Failed | Backup process failed | Check logs and config |

## System Health Checks

### Quick Health Check
```batch
# Run comprehensive system test
bin\run_daily_backup_new.bat test all-tests
```

### Component Health Checks
```batch
# Test shared libraries
tests\run_unit_tests.bat --suite common_functions

# Test integration
tests\integration\integration_tests.bat --type system

# Test performance
tests\performance\performance_benchmarks.bat --type startup
```

## Recovery Procedures

### Rollback to Old System
```batch
# Use migration utility to rollback
bin\migrate_to_new_system.bat
# Select option 4: Rollback to old system
```

### Reset Configuration
```batch
# Backup current config
copy tabletest\tables.json tabletest\tables.json.backup

# Reset to defaults (create minimal config)
echo [ > tabletest\tables.json
echo ] >> tabletest\tables.json

# Test with empty config
bin\run_daily_backup_new.bat test dry-run
```

### Clean Reinstall
```batch
# Backup important files
mkdir backup_before_reinstall
copy tabletest\tables.json backup_before_reinstall\
copy config\*.config backup_before_reinstall\ 2>nul

# Clean logs (optional)
rmdir /s /q logs

# Re-extract/reinstall system files
# Restore configuration
copy backup_before_reinstall\tables.json tabletest\

# Test system
bin\run_daily_backup_new.bat test dry-run
```

## Getting Help

### Self-Diagnosis
1. **Run dry-run test**: `bin\run_daily_backup_new.bat test dry-run`
2. **Check logs**: Look in `logs\` directory for recent error messages
3. **Verify configuration**: Ensure `tabletest\tables.json` exists and is valid
4. **Test components**: Run unit tests to verify system integrity

### Information to Collect
When reporting issues, include:
- **Command executed**: Exact command that failed
- **Error message**: Complete error output
- **Exit code**: Numeric exit code
- **Log files**: Recent log entries
- **System info**: Windows version, Python version
- **Configuration**: Table configuration file (sanitized)

### Diagnostic Commands
```batch
# System information
echo %OS%
python --version
dir bin\

# Configuration check
dir tabletest\tables.json
dir config\

# Recent logs
dir logs\ /s /o-d

# Test system
bin\run_daily_backup_new.bat test quick
```

## Prevention Tips

1. **Regular Testing**: Run `test dry-run` regularly
2. **Monitor Logs**: Check logs for warnings
3. **Keep Backups**: Backup configuration files
4. **Update Documentation**: Keep table lists current
5. **Test Changes**: Always test with `--dry-run` first
6. **Monitor Resources**: Watch disk space and performance
7. **Validate Configuration**: Test JSON files after changes
