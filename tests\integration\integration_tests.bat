@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Integration Test Suite                          S4NG-7
REM ===============================================================
REM Comprehensive integration tests for TNGD backup system
REM - End-to-end workflow testing
REM - Module interaction validation
REM - System integration verification
REM - Performance and reliability testing
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Load test framework
call "tests\unit\test_framework.bat"

REM Integration test variables
set "INTEGRATION_TEST_COUNT=0"
set "INTEGRATION_PASSED=0"
set "INTEGRATION_FAILED=0"
set "TEST_DATA_DIR=tests\test_data"
set "TEMP_TEST_DIR=tests\temp_integration"

REM ===============================================================
REM MAIN INTEGRATION TEST EXECUTION
REM ===============================================================

:main_integration
call :init_test_framework

echo ===============================================================
echo TNGD INTEGRATION TEST SUITE
echo ===============================================================
echo.

REM Setup test environment
call :setup_integration_environment

REM Process command line arguments
set "TEST_TYPE=all"
set "CLEANUP_AFTER=true"

:parse_integration_args
if "%~1"=="" goto integration_args_done

if /i "%~1"=="--type" (
    if not "%~2"=="" (
        set "TEST_TYPE=%~2"
        shift & shift & goto parse_integration_args
    )
)

if /i "%~1"=="--no-cleanup" (
    set "CLEANUP_AFTER=false"
    shift & goto parse_integration_args
)

if /i "%~1"=="--help" goto show_integration_help

shift & goto parse_integration_args

:integration_args_done

REM Execute integration tests based on type
if /i "%TEST_TYPE%"=="all" call :run_all_integration_tests
if /i "%TEST_TYPE%"=="workflow" call :test_backup_workflows
if /i "%TEST_TYPE%"=="modules" call :test_module_interactions
if /i "%TEST_TYPE%"=="system" call :test_system_integration
if /i "%TEST_TYPE%"=="performance" call :test_performance_integration

REM Show results
call :show_integration_results

REM Cleanup if requested
if "%CLEANUP_AFTER%"=="true" call :cleanup_integration_environment

exit /b %overall_result%

REM ===============================================================
REM TEST ENVIRONMENT SETUP
REM ===============================================================

:setup_integration_environment
echo Setting up integration test environment...

REM Create test directories
if not exist "%TEST_DATA_DIR%" mkdir "%TEST_DATA_DIR%" 2>nul
if not exist "%TEMP_TEST_DIR%" mkdir "%TEMP_TEST_DIR%" 2>nul

REM Create mock configuration files
call :create_test_config_files

REM Create mock table data
call :create_test_table_data

echo ✅ Integration test environment ready
echo.

goto :eof

:create_test_config_files
REM Create test configuration files
set "test_config=%TEST_DATA_DIR%\test_tables.json"
echo [ > "%test_config%"
echo   "test.app.table1", >> "%test_config%"
echo   "test.app.table2", >> "%test_config%"
echo   "test.app.table3" >> "%test_config%"
echo ] >> "%test_config%"

REM Create test environment config
set "env_config=%TEST_DATA_DIR%\test_env.config"
echo # Test Environment Configuration > "%env_config%"
echo CHUNK_SIZE=10000 >> "%env_config%"
echo TIMEOUT=300 >> "%env_config%"
echo RETRY_COUNT=2 >> "%env_config%"
echo DRY_RUN=true >> "%env_config%"

goto :eof

:create_test_table_data
REM Create mock table data for testing
set "table_data=%TEST_DATA_DIR%\sample_data.csv"
echo id,name,value > "%table_data%"
echo 1,test1,100 >> "%table_data%"
echo 2,test2,200 >> "%table_data%"
echo 3,test3,300 >> "%table_data%"

goto :eof

REM ===============================================================
REM INTEGRATION TEST SUITES
REM ===============================================================

:run_all_integration_tests
echo Running all integration tests...
echo.

call :test_backup_workflows
call :test_module_interactions
call :test_system_integration
call :test_error_scenarios

goto :eof

:test_backup_workflows
echo.
echo ===============================================================
echo TESTING BACKUP WORKFLOWS
echo ===============================================================

call :test_daily_backup_workflow
call :test_single_table_workflow
call :test_monthly_backup_workflow

goto :eof

:test_daily_backup_workflow
echo Testing daily backup workflow...

REM Test dry run workflow
call :integration_test "Daily Backup Dry Run" "bin\run_daily_backup_new.bat --dry-run" "0"

REM Test help system
call :integration_test "Daily Backup Help" "bin\run_daily_backup_new.bat help" "0"

REM Test invalid parameters
call :integration_test "Daily Backup Invalid Params" "bin\run_daily_backup_new.bat --invalid-param" "1"

goto :eof

:test_single_table_workflow
echo Testing single table workflow...

REM Test table listing (may fail if no real config, but should handle gracefully)
call :integration_test "Single Table List" "bin\run_daily_backup_new.bat single-table --list-tables" "0,1"

REM Test single table help
call :integration_test "Single Table Help" "bin\run_daily_backup_new.bat single-table" "2"

goto :eof

:test_monthly_backup_workflow
echo Testing monthly backup workflow...

REM Test monthly backup help
call :integration_test "Monthly Backup Help" "bin\run_monthly_backup_enhanced.bat help" "0"

REM Test monthly backup with invalid month
call :integration_test "Monthly Backup Invalid Month" "bin\run_monthly_backup_enhanced.bat" "2"

goto :eof

:test_module_interactions
echo.
echo ===============================================================
echo TESTING MODULE INTERACTIONS
echo ===============================================================

call :test_shared_library_integration
call :test_logging_integration
call :test_config_integration

goto :eof

:test_shared_library_integration
echo Testing shared library integration...

REM Test that modules can load shared libraries
call :integration_test "Load Common Functions" "call bin\shared\common_functions.bat" "0"
call :integration_test "Load Error Handling" "call bin\shared\error_handling.bat" "0"
call :integration_test "Load Logging" "call bin\shared\logging.bat" "0"
call :integration_test "Load Config Manager" "call bin\shared\config_manager.bat" "0"

goto :eof

:test_logging_integration
echo Testing logging integration...

REM Test log file creation and writing
set "test_log=%TEMP_TEST_DIR%\integration_test.log"
call "bin\shared\logging.bat"
call :init_logging "%test_log%" "IntegrationTest" 20

call :log_info "Integration test message"
call :assert_file_exists "%test_log%" "Integration logging creates log file"

goto :eof

:test_config_integration
echo Testing configuration integration...

REM Test configuration discovery
call "bin\shared\config_manager.bat"
call :load_default_config

REM Verify defaults are loaded
if not "%DEFAULT_CHUNK_SIZE%"=="" (
    call :integration_test_pass "Configuration defaults loaded"
) else (
    call :integration_test_fail "Configuration defaults not loaded"
)

goto :eof

:test_system_integration
echo.
echo ===============================================================
echo TESTING SYSTEM INTEGRATION
echo ===============================================================

call :test_test_framework_integration
call :test_setup_integration

goto :eof

:test_test_framework_integration
echo Testing test framework integration...

REM Test that test framework works
call :integration_test "Test Framework Dry Run" "bin\run_daily_backup_new.bat test dry-run" "0,1"

goto :eof

:test_setup_integration
echo Testing setup integration...

REM Test setup help (should work without admin rights)
call :integration_test "Setup Help" "bin\run_daily_backup_new.bat setup help" "0"

goto :eof

:test_error_scenarios
echo.
echo ===============================================================
echo TESTING ERROR SCENARIOS
echo ===============================================================

call :test_missing_files
call :test_invalid_configurations
call :test_permission_errors

goto :eof

:test_missing_files
echo Testing missing file scenarios...

REM Test with non-existent script (should be handled gracefully)
call :integration_test "Missing Script Handling" "bin\non_existent_script.bat" "1"

goto :eof

:test_invalid_configurations
echo Testing invalid configuration scenarios...

REM Test with invalid JSON (create temporarily)
set "invalid_config=%TEMP_TEST_DIR%\invalid.json"
echo { invalid json > "%invalid_config%"

REM This should be handled gracefully by the system
echo ✅ Invalid configuration test setup complete

goto :eof

:test_permission_errors
echo Testing permission error scenarios...

REM Test scenarios that might require elevated permissions
echo ✅ Permission error test scenarios noted (require manual testing)

goto :eof

REM ===============================================================
REM INTEGRATION TEST UTILITIES
REM ===============================================================

:integration_test
REM Usage: call :integration_test "test_name" "command" "expected_exit_codes"
setlocal
set "test_name=%~1"
set "command=%~2"
set "expected_codes=%~3"

set /a "INTEGRATION_TEST_COUNT+=1"

echo Testing: %test_name%

REM Execute command and capture exit code
%command% >nul 2>&1
set "actual_code=!ERRORLEVEL!"

REM Check if exit code is in expected list
call :check_exit_code_in_list "!actual_code!" "%expected_codes%" result

if "!result!"=="true" (
    call :integration_test_pass "%test_name%"
) else (
    call :integration_test_fail "%test_name%" "Expected: %expected_codes%, Got: !actual_code!"
)

goto :eof

:check_exit_code_in_list
REM Usage: call :check_exit_code_in_list "actual_code" "expected_list" result_var
setlocal
set "actual=%~1"
set "expected_list=%~2"
set "found=false"

REM Check each expected code
for %%a in (%expected_list%) do (
    if "%%a"=="%actual%" set "found=true"
)

endlocal & set "%~3=%found%"
goto :eof

:integration_test_pass
set "test_name=%~1"
set /a "INTEGRATION_PASSED+=1"
echo ✅ PASS: %test_name%
call :log_test "INTEGRATION PASS: %test_name%"
goto :eof

:integration_test_fail
set "test_name=%~1"
set "details=%~2"
set /a "INTEGRATION_FAILED+=1"
echo ❌ FAIL: %test_name%
if not "%details%"=="" echo    Details: %details%
call :log_test "INTEGRATION FAIL: %test_name% - %details%"
goto :eof

:show_integration_results
echo.
echo ===============================================================
echo INTEGRATION TEST RESULTS
echo ===============================================================
echo Total Integration Tests: %INTEGRATION_TEST_COUNT%
echo Passed: %INTEGRATION_PASSED%
echo Failed: %INTEGRATION_FAILED%
echo.

if %INTEGRATION_FAILED% EQU 0 (
    echo ✅ ALL INTEGRATION TESTS PASSED!
    set "overall_result=0"
) else (
    echo ❌ %INTEGRATION_FAILED% INTEGRATION TESTS FAILED
    set "overall_result=1"
)

echo.
echo Integration Test Log: %TEST_LOG_FILE%
echo ===============================================================

goto :eof

:cleanup_integration_environment
echo.
echo Cleaning up integration test environment...

REM Remove temporary test directories and files
if exist "%TEMP_TEST_DIR%" rmdir /s /q "%TEMP_TEST_DIR%" 2>nul
if exist "%TEST_DATA_DIR%" rmdir /s /q "%TEST_DATA_DIR%" 2>nul

echo ✅ Integration test cleanup completed

goto :eof

:show_integration_help
echo ===============================================================
echo TNGD Integration Test Suite - Help
echo ===============================================================
echo.
echo Usage:
echo   integration_tests.bat [options]
echo.
echo Options:
echo   --type TYPE           Run specific test type (default: all)
echo   --no-cleanup          Skip cleanup after tests
echo   --help                Show this help message
echo.
echo Available Test Types:
echo   all                   Run all integration tests
echo   workflow              Test backup workflows
echo   modules               Test module interactions
echo   system                Test system integration
echo   performance           Test performance scenarios
echo.
echo Examples:
echo   integration_tests.bat
echo   integration_tests.bat --type workflow
echo   integration_tests.bat --no-cleanup
echo.
echo ===============================================================

exit /b 0
