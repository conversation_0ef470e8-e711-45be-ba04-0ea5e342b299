#!/usr/bin/env python3
"""
Monthly Backup Configuration Module

This module provides configuration management specifically for monthly backup operations,
completely separate from daily backup configurations to prevent confusion and conflicts.

Features:
- Monthly-specific configuration validation
- Historical date range validation
- Monthly storage path configuration
- Monthly retry and timeout settings
- Monthly resource management settings
"""

import datetime
import calendar
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

from core.config_manager import ConfigManager
from utils.minimal_logging import logger


@dataclass
class MonthlyBackupSettings:
    """Monthly backup specific settings."""
    # Target month/year settings
    target_month: int
    target_year: int
    
    # Processing settings
    max_retries: int = 3
    retry_delay_seconds: int = 60
    timeout_seconds: int = 1800  # 30 minutes per day
    chunk_size: int = 1000
    
    # Resource management
    max_concurrent_days: int = 1  # Process days sequentially for monthly
    memory_limit_mb: int = 2048
    disk_space_threshold_gb: int = 10
    
    # Storage settings
    storage_prefix: str = "monthly_backup"
    compression_algorithm: str = "tar.gz"
    
    # Recovery settings
    enable_checkpoints: bool = True
    checkpoint_interval_days: int = 5
    auto_resume: bool = True
    
    # Validation settings
    validate_historical_data: bool = True
    skip_empty_days: bool = False
    
    def __post_init__(self):
        """Validate settings after initialization."""
        if not (1 <= self.target_month <= 12):
            raise ValueError(f"Invalid target month: {self.target_month}")
        
        if self.target_year < 2020 or self.target_year > 2030:
            raise ValueError(f"Invalid target year: {self.target_year}")
        
        # Validate that target month/year is not in the future
        target_date = datetime.date(self.target_year, self.target_month, 1)
        current_date = datetime.date.today()
        if target_date > current_date:
            logger.warning(f"Target month {calendar.month_name[self.target_month]} {self.target_year} is in the future")
    
    @property
    def days_in_month(self) -> int:
        """Get number of days in the target month."""
        return calendar.monthrange(self.target_year, self.target_month)[1]
    
    @property
    def month_name(self) -> str:
        """Get the name of the target month."""
        return calendar.month_name[self.target_month]
    
    @property
    def date_range(self) -> List[datetime.date]:
        """Get list of all dates in the target month."""
        return [
            datetime.date(self.target_year, self.target_month, day)
            for day in range(1, self.days_in_month + 1)
        ]


@dataclass
class MonthlyTableConfig:
    """Configuration for tables in monthly backup."""
    table_names: List[str]
    table_config_path: Optional[str] = None
    
    # Table-specific settings
    table_timeout_seconds: int = 300  # 5 minutes per table
    table_retry_count: int = 2
    
    # Data validation
    validate_table_structure: bool = True
    check_data_consistency: bool = True
    
    def __post_init__(self):
        """Validate table configuration."""
        if not self.table_names:
            raise ValueError("Table names list cannot be empty")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tables = []
        for table in self.table_names:
            if table not in seen:
                seen.add(table)
                unique_tables.append(table)
        self.table_names = unique_tables
        
        if len(self.table_names) != len(set(self.table_names)):
            logger.warning("Duplicate table names found and removed")


class MonthlyBackupConfigManager:
    """
    Configuration manager specifically for monthly backup operations.
    
    This manager handles all monthly backup configuration needs and is
    completely separate from daily backup configuration management.
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the monthly backup configuration manager.
        
        Args:
            config_manager: Optional base configuration manager
        """
        self.config_manager = config_manager or ConfigManager()
        self.monthly_config = self.config_manager.get('backup', 'monthly_backup', {})
        
        logger.info("MonthlyBackupConfigManager initialized")
    
    def create_monthly_settings(self, month: int, year: int, 
                              custom_settings: Optional[Dict[str, Any]] = None) -> MonthlyBackupSettings:
        """
        Create monthly backup settings for the specified month and year.
        
        Args:
            month: Target month (1-12)
            year: Target year
            custom_settings: Optional custom settings to override defaults
            
        Returns:
            MonthlyBackupSettings instance
        """
        # Start with default settings from config
        settings_dict = {
            'target_month': month,
            'target_year': year,
            'max_retries': self.monthly_config.get('max_retries', 3),
            'retry_delay_seconds': self.monthly_config.get('retry_delay_seconds', 60),
            'timeout_seconds': self.monthly_config.get('timeout_seconds', 1800),
            'chunk_size': self.monthly_config.get('chunk_size', 1000),
            'max_concurrent_days': self.monthly_config.get('max_concurrent_days', 1),
            'memory_limit_mb': self.monthly_config.get('memory_limit_mb', 2048),
            'disk_space_threshold_gb': self.monthly_config.get('disk_space_threshold_gb', 10),
            'storage_prefix': self.monthly_config.get('storage_prefix', 'monthly_backup'),
            'compression_algorithm': self.monthly_config.get('compression_algorithm', 'tar.gz'),
            'enable_checkpoints': self.monthly_config.get('enable_checkpoints', True),
            'checkpoint_interval_days': self.monthly_config.get('checkpoint_interval_days', 5),
            'auto_resume': self.monthly_config.get('auto_resume', True),
            'validate_historical_data': self.monthly_config.get('validate_historical_data', True),
            'skip_empty_days': self.monthly_config.get('skip_empty_days', False)
        }
        
        # Apply custom settings if provided
        if custom_settings:
            settings_dict.update(custom_settings)
        
        return MonthlyBackupSettings(**settings_dict)
    
    def create_table_config(self, table_names: Optional[List[str]] = None,
                          table_config_path: Optional[str] = None) -> MonthlyTableConfig:
        """
        Create table configuration for monthly backup.
        
        Args:
            table_names: Optional list of table names
            table_config_path: Optional path to table configuration file
            
        Returns:
            MonthlyTableConfig instance
        """
        # Load table names if not provided
        if table_names is None:
            table_names = self._load_table_names(table_config_path)
        
        table_settings = self.monthly_config.get('table_settings', {})
        
        return MonthlyTableConfig(
            table_names=table_names,
            table_config_path=table_config_path,
            table_timeout_seconds=table_settings.get('table_timeout_seconds', 300),
            table_retry_count=table_settings.get('table_retry_count', 2),
            validate_table_structure=table_settings.get('validate_table_structure', True),
            check_data_consistency=table_settings.get('check_data_consistency', True)
        )
    
    def _load_table_names(self, config_path: Optional[str] = None) -> List[str]:
        """
        Load table names from configuration file.
        
        Args:
            config_path: Optional path to table configuration file
            
        Returns:
            List of table names
        """
        # Try specified path first
        if config_path:
            table_names = self._load_from_file(config_path)
            if table_names:
                return table_names
        
        # Try default monthly table config paths
        default_paths = [
            'tabletest/tables.json',
            'config/monthly_tables.json',
            'config/tables.json'
        ]
        
        for path in default_paths:
            table_names = self._load_from_file(path)
            if table_names:
                logger.info(f"Loaded {len(table_names)} tables from {path}")
                return table_names
        
        # Fallback to default tables
        logger.warning("Could not load table configuration, using default tables")
        return ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
    
    def _load_from_file(self, file_path: str) -> Optional[List[str]]:
        """
        Load table names from a specific file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            List of table names or None if failed
        """
        try:
            import json
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'tables' in data:
                return data['tables']
            else:
                logger.warning(f"Invalid table configuration format in {file_path}")
                return None
                
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            logger.debug(f"Could not load table configuration from {file_path}: {str(e)}")
            return None
    
    def generate_storage_path(self, table_name: str, target_date: datetime.date,
                            settings: MonthlyBackupSettings) -> str:
        """
        Generate storage path for monthly backup.
        
        Args:
            table_name: Name of the table
            target_date: Target backup date
            settings: Monthly backup settings
            
        Returns:
            Storage path string
        """
        return (f"{settings.storage_prefix}/{settings.target_year}/"
                f"{settings.month_name}/{target_date.strftime('%Y-%m-%d')}/"
                f"{table_name}_{target_date.strftime('%Y%m%d')}.{settings.compression_algorithm}")
    
    def validate_monthly_config(self, settings: MonthlyBackupSettings,
                              table_config: MonthlyTableConfig) -> Dict[str, Any]:
        """
        Validate monthly backup configuration.
        
        Args:
            settings: Monthly backup settings
            table_config: Table configuration
            
        Returns:
            Validation results dictionary
        """
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate date range
        try:
            date_range = settings.date_range
            if not date_range:
                validation_results['errors'].append("Invalid date range")
                validation_results['valid'] = False
        except Exception as e:
            validation_results['errors'].append(f"Date validation error: {str(e)}")
            validation_results['valid'] = False
        
        # Validate table configuration
        if not table_config.table_names:
            validation_results['errors'].append("No tables configured for backup")
            validation_results['valid'] = False
        
        # Validate resource settings
        if settings.memory_limit_mb < 512:
            validation_results['warnings'].append("Memory limit is very low")
        
        if settings.disk_space_threshold_gb < 5:
            validation_results['warnings'].append("Disk space threshold is very low")
        
        return validation_results
