@echo off
REM ===============================================================
REM TNGD Configuration Manager                           S4NG-7
REM ===============================================================
REM Centralized configuration management for TNGD backup system
REM - Configuration file discovery with fallbacks
REM - Parameter validation and defaults
REM - Environment-specific settings
REM - Configuration validation
REM ===============================================================

REM Load dependencies
if not defined TNGD_COMMON_LOADED call "%~dp0common_functions.bat"
if not defined TNGD_LOGGING_LOADED call "%~dp0logging.bat"

REM ===============================================================
REM CONFIGURATION PATHS AND DEFAULTS
REM ===============================================================

REM Default configuration values
set DEFAULT_CHUNK_SIZE=100000
set DEFAULT_TIMEOUT=1800
set DEFAULT_RETRY_COUNT=3
set DEFAULT_LOG_RETENTION_DAYS=30
set DEFAULT_MIN_DISK_SPACE_GB=5
set DEFAULT_BACKUP_TIME=01:00
set DEFAULT_PYTHON_CMD=python

REM Configuration file search paths (in order of preference)
set CONFIG_PATHS[0]=tabletest\tables.json
set CONFIG_PATHS[1]=config\tables.json
set CONFIG_PATHS[2]=backup\tables.json
set CONFIG_PATHS[3]=config\backup_tables.json

REM ===============================================================
REM CONFIGURATION DISCOVERY
REM ===============================================================

:find_table_config
REM Usage: call :find_table_config config_path_var
setlocal enabledelayedexpansion
set "found_config="

call :log_info "Searching for table configuration file..."

REM Try each configuration path
for /L %%i in (0,1,3) do (
    if defined CONFIG_PATHS[%%i] (
        set "test_path=!CONFIG_PATHS[%%i]!"
        if exist "!test_path!" (
            call :log_info "Found table configuration: !test_path!"
            set "found_config=!test_path!"
            goto config_found
        ) else (
            call :log_debug "Configuration not found: !test_path!"
        )
    )
)

:config_found
if "%found_config%"=="" (
    call :log_error "No table configuration file found in any search path"
    call :log_error "Searched paths:"
    for /L %%i in (0,1,3) do (
        if defined CONFIG_PATHS[%%i] (
            call :log_error "  - !CONFIG_PATHS[%%i]!"
        )
    )
    exit /b 1
)

endlocal & set "%~1=%found_config%"
goto :eof

:validate_table_config
REM Usage: call :validate_table_config "config_file_path"
setlocal
set "config_file=%~1"

call :log_info "Validating table configuration: %config_file%"

REM Check if file exists
if not exist "%config_file%" (
    call :log_error "Configuration file not found: %config_file%"
    exit /b 1
)

REM Check if file is readable
type "%config_file%" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    call :log_error "Cannot read configuration file: %config_file%"
    exit /b 1
)

REM Validate JSON format
call :log_info "Validating JSON format..."
%PYTHON_CMD% -c "import json; json.load(open(r'%config_file%')); print('Configuration file is valid JSON')" 2>nul
if %ERRORLEVEL% NEQ 0 (
    call :log_warning "JSON validation failed, but file may still be usable"
    REM Don't exit - continue with backup as file might still be valid
)

REM Count tables in configuration
call :count_json_tables "%config_file%" table_count
call :log_info "Found %table_count% tables in configuration"

if %table_count% EQU 0 (
    call :log_warning "No tables found in configuration file"
)

call :log_info "Configuration validation completed"
goto :eof

REM ===============================================================
REM PARAMETER MANAGEMENT
REM ===============================================================

:load_default_config
REM Usage: call :load_default_config
call :log_info "Loading default configuration values..."

REM Set defaults if not already defined
if not defined CHUNK_SIZE set CHUNK_SIZE=%DEFAULT_CHUNK_SIZE%
if not defined TIMEOUT set TIMEOUT=%DEFAULT_TIMEOUT%
if not defined RETRY_COUNT set RETRY_COUNT=%DEFAULT_RETRY_COUNT%
if not defined LOG_RETENTION_DAYS set LOG_RETENTION_DAYS=%DEFAULT_LOG_RETENTION_DAYS%
if not defined MIN_DISK_SPACE_GB set MIN_DISK_SPACE_GB=%DEFAULT_MIN_DISK_SPACE_GB%
if not defined BACKUP_TIME set BACKUP_TIME=%DEFAULT_BACKUP_TIME%
if not defined PYTHON_CMD set PYTHON_CMD=%DEFAULT_PYTHON_CMD%

REM Backup-specific defaults
if not defined DRY_RUN set DRY_RUN=false
if not defined VERBOSE set VERBOSE=false
if not defined FORCE_EMAIL set FORCE_EMAIL=false
if not defined SKIP_HEALTH_CHECKS set SKIP_HEALTH_CHECKS=false

call :log_info "Default configuration loaded"
goto :eof

:validate_config_parameters
REM Usage: call :validate_config_parameters
call :log_info "Validating configuration parameters..."

REM Validate numeric parameters
call :validate_numeric_param CHUNK_SIZE %DEFAULT_CHUNK_SIZE% 1000 10000000
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_numeric_param TIMEOUT %DEFAULT_TIMEOUT% 60 7200
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_numeric_param RETRY_COUNT %DEFAULT_RETRY_COUNT% 1 10
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_numeric_param LOG_RETENTION_DAYS %DEFAULT_LOG_RETENTION_DAYS% 1 365
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_numeric_param MIN_DISK_SPACE_GB %DEFAULT_MIN_DISK_SPACE_GB% 1 1000
if %ERRORLEVEL% NEQ 0 exit /b 1

REM Validate boolean parameters
call :validate_boolean_param DRY_RUN
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_boolean_param VERBOSE
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_boolean_param FORCE_EMAIL
if %ERRORLEVEL% NEQ 0 exit /b 1

call :validate_boolean_param SKIP_HEALTH_CHECKS
if %ERRORLEVEL% NEQ 0 exit /b 1

REM Validate time format
call :validate_time_param BACKUP_TIME
if %ERRORLEVEL% NEQ 0 exit /b 1

call :log_info "Configuration parameter validation completed"
goto :eof

:validate_numeric_param
REM Usage: call :validate_numeric_param param_name default_value min_value max_value
setlocal enabledelayedexpansion
set "param_name=%~1"
set "default_value=%~2"
set "min_value=%~3"
set "max_value=%~4"

REM Get current value
call set "current_value=%%%param_name%%%"

REM Check if value is numeric
echo %current_value%| findstr /r "^[0-9][0-9]*$" >nul
if %ERRORLEVEL% NEQ 0 (
    call :log_warning "Invalid numeric value for %param_name%: %current_value%, using default: %default_value%"
    endlocal & set "%param_name%=%default_value%"
    goto :eof
)

REM Check range
if %current_value% LSS %min_value% (
    call :log_warning "%param_name% value %current_value% below minimum %min_value%, using default: %default_value%"
    endlocal & set "%param_name%=%default_value%"
    goto :eof
)

if %current_value% GTR %max_value% (
    call :log_warning "%param_name% value %current_value% above maximum %max_value%, using default: %default_value%"
    endlocal & set "%param_name%=%default_value%"
    goto :eof
)

call :log_debug "%param_name% validation passed: %current_value%"
goto :eof

:validate_boolean_param
REM Usage: call :validate_boolean_param param_name
setlocal enabledelayedexpansion
set "param_name=%~1"

REM Get current value
call set "current_value=%%%param_name%%%"

REM Convert to lowercase for comparison
set "lower_value=%current_value%"
if "%lower_value%"=="TRUE" set "lower_value=true"
if "%lower_value%"=="FALSE" set "lower_value=false"
if "%lower_value%"=="1" set "lower_value=true"
if "%lower_value%"=="0" set "lower_value=false"

if "%lower_value%"=="true" (
    endlocal & set "%param_name%=true"
    goto :eof
)

if "%lower_value%"=="false" (
    endlocal & set "%param_name%=false"
    goto :eof
)

call :log_warning "Invalid boolean value for %param_name%: %current_value%, using default: false"
endlocal & set "%param_name%=false"
goto :eof

:validate_time_param
REM Usage: call :validate_time_param param_name
setlocal enabledelayedexpansion
set "param_name=%~1"

REM Get current value
call set "current_value=%%%param_name%%%"

REM Validate HH:MM format
echo %current_value%| findstr /r "^[0-2][0-9]:[0-5][0-9]$" >nul
if %ERRORLEVEL% NEQ 0 (
    call :log_warning "Invalid time format for %param_name%: %current_value%, using default: %DEFAULT_BACKUP_TIME%"
    endlocal & set "%param_name%=%DEFAULT_BACKUP_TIME%"
    exit /b 1
)

call :log_debug "%param_name% time validation passed: %current_value%"
goto :eof

REM ===============================================================
REM ENVIRONMENT-SPECIFIC CONFIGURATION
REM ===============================================================

:load_environment_config
REM Usage: call :load_environment_config [environment_name]
setlocal
set "environment=%~1"

if "%environment%"=="" set "environment=default"

call :log_info "Loading environment-specific configuration: %environment%"

REM Look for environment-specific config file
set "env_config=config\%environment%.config"
if exist "%env_config%" (
    call :log_info "Found environment config: %env_config%"
    call :load_config_file "%env_config%"
) else (
    call :log_info "No environment-specific config found, using defaults"
)

goto :eof

:load_config_file
REM Usage: call :load_config_file "config_file_path"
setlocal enabledelayedexpansion
set "config_file=%~1"

call :log_info "Loading configuration from: %config_file%"

if not exist "%config_file%" (
    call :log_warning "Configuration file not found: %config_file%"
    goto :eof
)

REM Read configuration file (simple key=value format)
for /f "usebackq tokens=1,2 delims==" %%a in ("%config_file%") do (
    set "key=%%a"
    set "value=%%b"
    
    REM Skip comments and empty lines
    if not "!key:~0,1!"=="#" if not "!key!"=="" (
        call :log_debug "Setting !key!=!value!"
        endlocal & set "!key!=!value!" & setlocal enabledelayedexpansion
    )
)

call :log_info "Configuration file loaded successfully"
goto :eof

REM ===============================================================
REM CONFIGURATION DISPLAY
REM ===============================================================

:show_current_config
REM Usage: call :show_current_config
echo ===============================================================
echo CURRENT CONFIGURATION
echo ===============================================================
echo Chunk Size: %CHUNK_SIZE%
echo Timeout: %TIMEOUT% seconds
echo Retry Count: %RETRY_COUNT%
echo Log Retention: %LOG_RETENTION_DAYS% days
echo Min Disk Space: %MIN_DISK_SPACE_GB% GB
echo Backup Time: %BACKUP_TIME%
echo Python Command: %PYTHON_CMD%
echo.
echo Flags:
echo - Dry Run: %DRY_RUN%
echo - Verbose: %VERBOSE%
echo - Force Email: %FORCE_EMAIL%
echo - Skip Health Checks: %SKIP_HEALTH_CHECKS%
echo ===============================================================
goto :eof

:export_config
REM Usage: call :export_config "output_file"
setlocal
set "output_file=%~1"

if "%output_file%"=="" (
    call :get_timestamp timestamp
    set "output_file=config\exported_config_%timestamp%.config"
)

call :log_info "Exporting current configuration to: %output_file%"

REM Ensure output directory exists
for %%F in ("%output_file%") do (
    if not exist "%%~dpF" mkdir "%%~dpF" 2>nul
)

REM Export configuration
echo # TNGD Backup System Configuration > "%output_file%"
echo # Generated: %date% %time% >> "%output_file%"
echo. >> "%output_file%"
echo CHUNK_SIZE=%CHUNK_SIZE% >> "%output_file%"
echo TIMEOUT=%TIMEOUT% >> "%output_file%"
echo RETRY_COUNT=%RETRY_COUNT% >> "%output_file%"
echo LOG_RETENTION_DAYS=%LOG_RETENTION_DAYS% >> "%output_file%"
echo MIN_DISK_SPACE_GB=%MIN_DISK_SPACE_GB% >> "%output_file%"
echo BACKUP_TIME=%BACKUP_TIME% >> "%output_file%"
echo PYTHON_CMD=%PYTHON_CMD% >> "%output_file%"
echo. >> "%output_file%"
echo DRY_RUN=%DRY_RUN% >> "%output_file%"
echo VERBOSE=%VERBOSE% >> "%output_file%"
echo FORCE_EMAIL=%FORCE_EMAIL% >> "%output_file%"
echo SKIP_HEALTH_CHECKS=%SKIP_HEALTH_CHECKS% >> "%output_file%"

call :log_info "Configuration exported successfully"
goto :eof
