@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Scheduler Setup Module                          S4NG-7
REM ===============================================================
REM Task scheduler management for TNGD backup system
REM - Create, list, and manage scheduled tasks
REM - Multiple backup schedule types
REM - Administrative privilege handling
REM - Task validation and monitoring
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM ===============================================================
REM INITIALIZATION
REM ===============================================================

:init_scheduler_setup
REM Initialize logging
call :get_timestamp timestamp
call :create_log_directory "setup" "%timestamp%" log_dir
set "LOG_FILE=%log_dir%\scheduler_setup_%timestamp%.log"
call :init_logging "%LOG_FILE%" "SchedulerSetup" %LOG_LEVEL_INFO%

call :log_operation_start "TNGD Scheduler Setup"

REM Load configuration
call :load_default_config

REM Process command line arguments
call :process_setup_arguments %*
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

goto :main_setup_process

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_setup_arguments
set "SETUP_COMMAND="
set "TASK_NAME="
set "BACKUP_TIME=%DEFAULT_BACKUP_TIME%"
set "DAY_OF_WEEK=SUN"
set "DAY_OF_MONTH=1"

REM Check if any arguments provided
if "%~1"=="" set "SETUP_COMMAND=help"

:parse_setup_args
if "%~1"=="" goto setup_args_done

if /i "%~1"=="help" (
    set SETUP_COMMAND=help
    shift & goto parse_setup_args
)

if /i "%~1"=="create-daily" (
    set SETUP_COMMAND=create-daily
    shift & goto parse_setup_args
)

if /i "%~1"=="create-weekly" (
    set SETUP_COMMAND=create-weekly
    shift & goto parse_setup_args
)

if /i "%~1"=="create-monthly" (
    set SETUP_COMMAND=create-monthly
    shift & goto parse_setup_args
)

if /i "%~1"=="create-all" (
    set SETUP_COMMAND=create-all
    shift & goto parse_setup_args
)

if /i "%~1"=="list" (
    set SETUP_COMMAND=list
    shift & goto parse_setup_args
)

if /i "%~1"=="status" (
    set SETUP_COMMAND=status
    if not "%~2"=="" (
        set TASK_NAME=%~2
        shift
    )
    shift & goto parse_setup_args
)

if /i "%~1"=="delete" (
    set SETUP_COMMAND=delete
    if not "%~2"=="" (
        set TASK_NAME=%~2
        shift
    )
    shift & goto parse_setup_args
)

if /i "%~1"=="--time" (
    if not "%~2"=="" (
        call :validate_safe_string "%~2" "time parameter"
        if !ERRORLEVEL! EQU 0 (
            set BACKUP_TIME=%~2
            call :log_info "Backup time set to: %~2"
        )
        shift & shift & goto parse_setup_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --time"
        exit /b %ERROR_INVALID_PARAM%
    )
)

if /i "%~1"=="--day" (
    if not "%~2"=="" (
        call :validate_safe_string "%~2" "day parameter"
        if !ERRORLEVEL! EQU 0 (
            set DAY_OF_WEEK=%~2
            set DAY_OF_MONTH=%~2
            call :log_info "Day parameter set to: %~2"
        )
        shift & shift & goto parse_setup_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --day"
        exit /b %ERROR_INVALID_PARAM%
    )
)

REM Unknown parameter
call :log_warning "Unknown setup parameter: %~1"
shift & goto parse_setup_args

:setup_args_done
goto :eof

REM ===============================================================
REM MAIN SETUP PROCESS
REM ===============================================================

:main_setup_process
call :log_info "Starting scheduler setup with command: %SETUP_COMMAND%"

echo ===============================================================
echo TNGD DAILY BACKUP SCHEDULER SETUP
echo ===============================================================
echo Command: %SETUP_COMMAND%
echo Log File: %LOG_FILE%
echo ===============================================================
echo.

REM Route to appropriate setup function
if "%SETUP_COMMAND%"=="help" goto show_setup_help
if "%SETUP_COMMAND%"=="create-daily" goto create_daily_task
if "%SETUP_COMMAND%"=="create-weekly" goto create_weekly_task
if "%SETUP_COMMAND%"=="create-monthly" goto create_monthly_task
if "%SETUP_COMMAND%"=="create-all" goto create_all_tasks
if "%SETUP_COMMAND%"=="list" goto list_tasks
if "%SETUP_COMMAND%"=="status" goto show_task_status
if "%SETUP_COMMAND%"=="delete" goto delete_task

REM Unknown setup command
call :handle_error %ERROR_INVALID_PARAM% "Unknown setup command: %SETUP_COMMAND%"
goto show_setup_help

REM ===============================================================
REM TASK CREATION FUNCTIONS
REM ===============================================================

:create_daily_task
call :log_info "Creating daily backup task..."
echo Creating daily backup task...

REM Check admin rights
call :check_admin_rights
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_ACCESS_DENIED% "Administrator rights required to create scheduled tasks"
    exit /b %ERROR_ACCESS_DENIED%
)

REM Validate scheduler script
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
call :validate_file_path "%SCHEDULER_SCRIPT%" "Scheduler script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Scheduler script not found: %SCHEDULER_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :log_info "Creating daily task with time: %BACKUP_TIME%"
call :safe_execute "%PYTHON_CMD% %SCHEDULER_SCRIPT% --create --time %BACKUP_TIME% --task-name TNGD_DailyBackup" "Daily task creation"
set task_exit_code=!ERRORLEVEL!

call :display_task_result "Daily backup task creation" %task_exit_code%
call :log_operation_end "Create Daily Task" %task_exit_code%
exit /b %task_exit_code%

:create_weekly_task
call :log_info "Creating weekly backup task..."
echo Creating weekly backup task...

REM Check admin rights
call :check_admin_rights
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_ACCESS_DENIED% "Administrator rights required to create scheduled tasks"
    exit /b %ERROR_ACCESS_DENIED%
)

call :log_info "Creating weekly task - Time: %BACKUP_TIME%, Day: %DAY_OF_WEEK%"

REM Create weekly task using schtasks
set "task_command=%~dp0..\run_daily_backup.bat --weekly"
call :safe_execute "schtasks /create /tn TNGD_WeeklyBackup /tr \"%task_command%\" /sc weekly /d %DAY_OF_WEEK% /st %BACKUP_TIME% /ru SYSTEM /rl HIGHEST /f" "Weekly task creation"
set task_exit_code=!ERRORLEVEL!

call :display_task_result "Weekly backup task creation" %task_exit_code%
call :log_operation_end "Create Weekly Task" %task_exit_code%
exit /b %task_exit_code%

:create_monthly_task
call :log_info "Creating monthly backup task..."
echo Creating monthly backup task...

REM Check admin rights
call :check_admin_rights
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_ACCESS_DENIED% "Administrator rights required to create scheduled tasks"
    exit /b %ERROR_ACCESS_DENIED%
)

call :log_info "Creating monthly task - Time: %BACKUP_TIME%, Day: %DAY_OF_MONTH%"

REM Create monthly task using schtasks
set "task_command=%~dp0run_monthly_backup_enhanced.bat"
call :safe_execute "schtasks /create /tn TNGD_MonthlyBackup /tr \"%task_command%\" /sc monthly /d %DAY_OF_MONTH% /st %BACKUP_TIME% /ru SYSTEM /rl HIGHEST /f" "Monthly task creation"
set task_exit_code=!ERRORLEVEL!

call :display_task_result "Monthly backup task creation" %task_exit_code%
call :log_operation_end "Create Monthly Task" %task_exit_code%
exit /b %task_exit_code%

:create_all_tasks
call :log_info "Creating all backup tasks..."
echo Creating all backup tasks...

REM Check admin rights
call :check_admin_rights
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_ACCESS_DENIED% "Administrator rights required to create scheduled tasks"
    exit /b %ERROR_ACCESS_DENIED%
)

REM Validate scheduler script
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
call :validate_file_path "%SCHEDULER_SCRIPT%" "Scheduler script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Scheduler script not found: %SCHEDULER_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %SCHEDULER_SCRIPT% --create-all" "All tasks creation"
set task_exit_code=!ERRORLEVEL!

call :display_task_result "All backup tasks creation" %task_exit_code%
call :log_operation_end "Create All Tasks" %task_exit_code%
exit /b %task_exit_code%

REM ===============================================================
REM TASK MANAGEMENT FUNCTIONS
REM ===============================================================

:list_tasks
call :log_info "Listing TNGD backup tasks..."
echo Listing TNGD backup tasks...

REM Check if scheduler script exists
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
if exist "%SCHEDULER_SCRIPT%" (
    call :safe_execute "%PYTHON_CMD% %SCHEDULER_SCRIPT% --list" "Task listing"
    set list_exit_code=!ERRORLEVEL!
) else (
    REM Fallback to schtasks command
    echo.
    echo ===============================================================
    echo TNGD BACKUP TASKS
    echo ===============================================================
    schtasks /query /tn "TNGD*" /fo table 2>nul
    if !ERRORLEVEL! NEQ 0 (
        echo No TNGD backup tasks found.
    )
    echo ===============================================================
    set list_exit_code=0
)

call :log_operation_end "List Tasks" %list_exit_code%
exit /b %list_exit_code%

:show_task_status
if "%TASK_NAME%"=="" set TASK_NAME=TNGD_DailyBackup

call :log_info "Checking status of task: %TASK_NAME%"
echo Checking backup task status: %TASK_NAME%

REM Check if scheduler script exists
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
if exist "%SCHEDULER_SCRIPT%" (
    call :safe_execute "%PYTHON_CMD% %SCHEDULER_SCRIPT% --status --task-name %TASK_NAME%" "Task status check"
    set status_exit_code=!ERRORLEVEL!
) else (
    REM Fallback to schtasks command
    echo.
    echo ===============================================================
    echo TASK STATUS: %TASK_NAME%
    echo ===============================================================
    schtasks /query /tn "%TASK_NAME%" /fo list 2>nul
    if !ERRORLEVEL! NEQ 0 (
        echo Task not found: %TASK_NAME%
        set status_exit_code=1
    ) else (
        set status_exit_code=0
    )
    echo ===============================================================
)

call :log_operation_end "Task Status" %status_exit_code%
exit /b %status_exit_code%

:delete_task
if "%TASK_NAME%"=="" set TASK_NAME=TNGD_DailyBackup

call :log_info "Deleting backup task: %TASK_NAME%"
echo Deleting backup task: %TASK_NAME%

echo WARNING: This will delete the backup task: %TASK_NAME%
set /p CONFIRM="Are you sure? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo Operation cancelled.
    call :log_info "Task deletion cancelled by user"
    exit /b 0
)

REM Check admin rights
call :check_admin_rights
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_ACCESS_DENIED% "Administrator rights required to delete scheduled tasks"
    exit /b %ERROR_ACCESS_DENIED%
)

REM Check if scheduler script exists
set SCHEDULER_SCRIPT=scripts\setup_daily_schedule.py
if exist "%SCHEDULER_SCRIPT%" (
    call :safe_execute "%PYTHON_CMD% %SCHEDULER_SCRIPT% --delete --task-name %TASK_NAME%" "Task deletion"
    set delete_exit_code=!ERRORLEVEL!
) else (
    REM Fallback to schtasks command
    call :safe_execute "schtasks /delete /tn \"%TASK_NAME%\" /f" "Task deletion"
    set delete_exit_code=!ERRORLEVEL!
)

call :display_task_result "Task deletion: %TASK_NAME%" %delete_exit_code%
call :log_operation_end "Delete Task" %delete_exit_code%
exit /b %delete_exit_code%

REM ===============================================================
REM UTILITY FUNCTIONS
REM ===============================================================

:display_task_result
setlocal
set "operation=%~1"
set "exit_code=%~2"

echo.
echo ===============================================================
echo OPERATION RESULT: %operation%
echo ===============================================================
echo Exit Code: %exit_code%
echo Completion Time: %date% %time%

if "%exit_code%"=="0" (
    echo Status: ✅ SUCCESS
    call :log_info "Operation SUCCESS: %operation%"
) else (
    echo Status: ❌ FAILED
    call :log_error "Operation FAILED: %operation% (Exit code: %exit_code%)"
)

echo ===============================================================

goto :eof

:show_setup_help
echo ===============================================================
echo TNGD Daily Backup Scheduler Setup - Help
echo ===============================================================
echo.
echo Usage:
echo   run_daily_backup.bat setup [command] [options]
echo.
echo Commands:
echo   create-daily      Create daily backup task
echo   create-weekly     Create weekly backup task
echo   create-monthly    Create monthly backup task
echo   create-all        Create all backup tasks
echo   list              List all TNGD backup tasks
echo   status [task]     Show status of backup task
echo   delete [task]     Delete backup task
echo   help              Show this help message
echo.
echo Options:
echo   --time HH:MM      Set backup time (default: %DEFAULT_BACKUP_TIME%)
echo   --day VALUE       Set day of week/month for weekly/monthly tasks
echo.
echo Examples:
echo   run_daily_backup.bat setup create-daily --time 02:30
echo   run_daily_backup.bat setup create-weekly --time 03:00 --day SUN
echo   run_daily_backup.bat setup create-monthly --day 1
echo   run_daily_backup.bat setup create-all
echo   run_daily_backup.bat setup list
echo   run_daily_backup.bat setup status TNGD_DailyBackup
echo   run_daily_backup.bat setup delete TNGD_DailyBackup
echo.
echo Notes:
echo   • Administrator privileges are required for task creation/deletion
echo   • Tasks run with SYSTEM privileges for maximum reliability
echo   • Use 'list' command to see all created TNGD backup tasks
echo   • Default task names: TNGD_DailyBackup, TNGD_WeeklyBackup, TNGD_MonthlyBackup
echo.
echo ===============================================================

call :log_operation_end "Setup Help" 0
exit /b 0
