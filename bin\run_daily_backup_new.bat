@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Daily Backup System - Main Entry Point         S4NG-7
REM ===============================================================
REM Refactored main entry point for TNGD backup system
REM - Clean, focused architecture
REM - Modular component design
REM - Consistent error handling
REM - Comprehensive logging
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM ===============================================================
REM INITIALIZATION
REM ===============================================================

:init_main_entry
REM Initialize basic logging (detailed logging will be handled by modules)
call :get_timestamp timestamp
set "MAIN_LOG_FILE=logs\main\main_entry_%timestamp%.log"
call :ensure_directory "logs\main"

REM Process command line arguments to determine mode
call :process_main_arguments %*
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

goto :route_to_module

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_main_arguments
set "MODE=backup"
set "REMAINING_ARGS="

REM Check if any arguments provided
if "%~1"=="" goto main_args_done

REM Determine mode from first argument
if /i "%~1"=="backup" (
    set MODE=backup
    shift
    goto collect_remaining_args
)

if /i "%~1"=="single-table" (
    set MODE=single-table
    shift
    goto collect_remaining_args
)

if /i "%~1"=="test" (
    set MODE=test
    shift
    goto collect_remaining_args
)

if /i "%~1"=="setup" (
    set MODE=setup
    shift
    goto collect_remaining_args
)

if /i "%~1"=="help" goto show_main_help
if /i "%~1"=="--help" goto show_main_help
if /i "%~1"=="-h" goto show_main_help

REM If first argument starts with --, treat as backup mode with options
if "%~1:~0,2%"=="--" (
    set MODE=backup
    goto collect_remaining_args
)

REM Default to backup mode
set MODE=backup

:collect_remaining_args
REM Collect all remaining arguments
:collect_loop
if "%~1"=="" goto main_args_done
if "%REMAINING_ARGS%"=="" (
    set "REMAINING_ARGS=%~1"
) else (
    set "REMAINING_ARGS=%REMAINING_ARGS% %~1"
)
shift
goto collect_loop

:main_args_done
goto :eof

REM ===============================================================
REM MODULE ROUTING
REM ===============================================================

:route_to_module
echo ===============================================================
echo TNGD DAILY BACKUP SYSTEM
echo ===============================================================
echo Mode: %MODE%
echo Time: %date% %time%
echo ===============================================================
echo.

REM Route to appropriate module
if "%MODE%"=="backup" goto route_to_backup
if "%MODE%"=="single-table" goto route_to_single_table
if "%MODE%"=="test" goto route_to_test
if "%MODE%"=="setup" goto route_to_setup

REM Should not reach here
call :handle_error %ERROR_GENERAL% "Unknown mode: %MODE%"
exit /b %ERROR_GENERAL%

:route_to_backup
call :log_info "Routing to daily backup module..."
call "bin\backup\daily_backup.bat" %REMAINING_ARGS%
set module_exit_code=!ERRORLEVEL!
goto module_completed

:route_to_single_table
call :log_info "Routing to single table backup module..."
call "bin\backup\single_table_backup.bat" %REMAINING_ARGS%
set module_exit_code=!ERRORLEVEL!
goto module_completed

:route_to_test
call :log_info "Routing to test runner module..."
call "bin\test\test_runner.bat" %REMAINING_ARGS%
set module_exit_code=!ERRORLEVEL!
goto module_completed

:route_to_setup
call :log_info "Routing to scheduler setup module..."
call "bin\setup\scheduler_setup.bat" %REMAINING_ARGS%
set module_exit_code=!ERRORLEVEL!
goto module_completed

:module_completed
echo.
echo ===============================================================
echo TNGD SYSTEM OPERATION COMPLETED
echo ===============================================================
echo Mode: %MODE%
echo Exit Code: %module_exit_code%
echo Completion Time: %date% %time%

REM Interpret exit codes
if %module_exit_code% EQU 0 (
    echo Status: ✅ SUCCESS
) else if %module_exit_code% EQU 1 (
    echo Status: ⚠️ COMPLETED WITH WARNINGS
) else (
    echo Status: ❌ FAILED
)

echo ===============================================================

exit /b %module_exit_code%

REM ===============================================================
REM HELP SYSTEM
REM ===============================================================

:show_main_help
echo ===============================================================
echo TNGD Daily Backup System - Unified Help
echo ===============================================================
echo.
echo This is the refactored daily backup system with modular architecture.
echo Each mode is handled by a dedicated, focused module.
echo.
echo Usage:
echo   run_daily_backup_new.bat [mode] [options]
echo.
echo MODES:
echo ===============================================================
echo.
echo 1. BACKUP MODE (default)
echo   run_daily_backup_new.bat [backup] [options]
echo
echo   Executes daily backup operations with comprehensive error handling,
echo   sequential table processing, and automatic cleanup.
echo
echo   Options:
echo     --dry-run          Validate tables only, no backup
echo     --single-table     Process only one table for testing
echo     --force-email      Send email even in test mode
echo     --verbose          Enable verbose logging
echo     --chunk-size N     Override default chunk size
echo     --timeout N        Override default timeout
echo.
echo   Examples:
echo     run_daily_backup_new.bat
echo     run_daily_backup_new.bat backup --dry-run
echo     run_daily_backup_new.bat --single-table --verbose
echo     run_daily_backup_new.bat --chunk-size 50000 --timeout 1800
echo.
echo 2. SINGLE TABLE MODE
echo   run_daily_backup_new.bat single-table ^<table_name^> [options]
echo
echo   Backup individual tables with flexible parameter handling.
echo
echo   Options:
echo     --list-tables      List all available tables
echo     --dry-run          Test mode - validate only, no backup
echo     --days N           Number of days to backup (default: 1)
echo     --chunk-size N     Chunk size for processing
echo     --verbose          Enable detailed logging
echo.
echo   Examples:
echo     run_daily_backup_new.bat single-table my.app.tngd.waf
echo     run_daily_backup_new.bat single-table --list-tables
echo     run_daily_backup_new.bat single-table my.app.tngd.waf --days 7
echo.
echo 3. TEST MODE
echo   run_daily_backup_new.bat test [test-type] [options]
echo
echo   Comprehensive testing framework with multiple test scenarios.
echo
echo   Test Types:
echo     dry-run            Validate tables only
echo     single-table       Test single table backup
echo     full-test [N]      Test complete backup process (limit to N tables)
echo     performance        Run performance benchmarks
echo     error-handling     Test error scenarios
echo     all-tests          Run comprehensive test suite
echo     quick              Run quick test suite
echo.
echo   Examples:
echo     run_daily_backup_new.bat test dry-run
echo     run_daily_backup_new.bat test single-table --verbose
echo     run_daily_backup_new.bat test full-test 3
echo     run_daily_backup_new.bat test all-tests
echo.
echo 4. SETUP MODE
echo   run_daily_backup_new.bat setup [command] [options]
echo
echo   Task scheduler management for automated backups.
echo
echo   Commands:
echo     create-daily       Create daily backup task
echo     create-weekly      Create weekly backup task
echo     create-monthly     Create monthly backup task
echo     create-all         Create all backup tasks
echo     list               List all TNGD backup tasks
echo     status [task]      Show status of backup task
echo     delete [task]      Delete backup task
echo.
echo   Examples:
echo     run_daily_backup_new.bat setup create-daily --time 02:30
echo     run_daily_backup_new.bat setup create-all
echo     run_daily_backup_new.bat setup list
echo.
echo ===============================================================
echo ARCHITECTURE IMPROVEMENTS:
echo ===============================================================
echo   ✅ Modular design with focused components
echo   ✅ Consistent error handling across all modules
echo   ✅ Centralized logging and configuration management
echo   ✅ Input validation and security hardening
echo   ✅ Comprehensive testing framework
echo   ✅ Standardized parameter processing
echo   ✅ Improved maintainability and debugging
echo.
echo ===============================================================
echo QUICK START:
echo ===============================================================
echo 1. Test the system:        run_daily_backup_new.bat test dry-run
echo 2. Run single table test:  run_daily_backup_new.bat test single-table
echo 3. Run daily backup:       run_daily_backup_new.bat
echo 4. Setup automation:       run_daily_backup_new.bat setup create-all
echo.
echo The refactored system provides the same functionality as the original
echo but with improved reliability, maintainability, and security.
echo.
echo ===============================================================

exit /b 0
