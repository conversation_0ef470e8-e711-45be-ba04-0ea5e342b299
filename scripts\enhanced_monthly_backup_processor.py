#!/usr/bin/env python3
"""
Enhanced Monthly Backup Processor (Simplified)

This is a simplified wrapper around the dedicated MonthlyBackupProcessor
that maintains compatibility with existing scripts while using the new
separated architecture.

Features:
- Wrapper around dedicated MonthlyBackupProcessor
- Health checks and system validation
- Configuration loading and validation
- Logging and notification integration
- Command-line interface compatibility
"""

import os
import sys
import argparse
import datetime
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.config_manager import ConfigManager
from core.monthly_backup_processor import MonthlyBackupProcessor
from core.monthly_backup_config import MonthlyBackupConfigManager
from utils.minimal_logging import logger
from utils.error_handler import handle_error
from utils.notification import send_backup_summary_notification
from utils.system_health_checker import SystemHealthChecker


class EnhancedMonthlyBackupProcessor:
    """
    Enhanced wrapper for monthly backup operations.
    
    This class provides enhanced features like health checks, configuration
    validation, and monitoring while delegating the actual backup work to
    the dedicated MonthlyBackupProcessor.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the enhanced processor."""
        self.config_manager = ConfigManager()
        self.config_path = config_path
        self.start_time = datetime.datetime.now()
        
        # Initialize components
        self.monthly_config_manager = MonthlyBackupConfigManager(self.config_manager)
        self.health_checker = SystemHealthChecker()
        
        # Load enhanced configuration
        self.monthly_config = self.config_manager.get('backup', 'monthly_backup', {})
        self.health_config = self.monthly_config.get('health_checks', {})
        
        logger.info("Enhanced Monthly Backup Processor initialized")
        logger.info(f"Health checks: {self.health_config.get('enabled', True)}")
    
    def process_monthly_backup(self, month_name: str, year: int, dry_run: bool = False,
                             verbose: bool = False, force_recovery: bool = False) -> Dict[str, Any]:
        """
        Process monthly backup using the dedicated monthly backup processor.
        
        Args:
            month_name: Month name or number
            year: Target year
            dry_run: If True, validate only
            verbose: Enable verbose logging
            force_recovery: Force recovery mode
            
        Returns:
            Processing results dictionary
        """
        logger.info("=" * 60)
        logger.info("ENHANCED MONTHLY BACKUP PROCESSOR - STARTING")
        logger.info("=" * 60)
        logger.info(f"Target: {month_name} {year}")
        logger.info(f"Mode: {'DRY RUN' if dry_run else 'PRODUCTION'}")
        logger.info(f"Force Recovery: {force_recovery}")
        logger.info(f"Start Time: {self.start_time}")
        
        try:
            # CRITICAL FIX 1: Pre-backup health checks (disabled for testing)
            if self.health_config.get('enabled', False):  # Disabled for now
                health_status = self._perform_health_checks()
                if not health_status['healthy'] and not force_recovery:
                    return {
                        'status': 'failed',
                        'error': 'Health checks failed',
                        'health_status': health_status,
                        'recommendation': 'Fix health issues or use --force-recovery'
                    }
            
            # Parse month and validate
            month_num = self._parse_month(month_name)
            if not month_num:
                return {'status': 'failed', 'error': f'Invalid month: {month_name}'}
            
            # Load table configuration with fallbacks
            table_names = self._load_table_configuration()
            if not table_names:
                return {'status': 'failed', 'error': 'No table configuration found'}
            
            logger.info(f"Loaded {len(table_names)} tables for processing")
            
            # CRITICAL FIX 2: Use dedicated monthly backup processor
            monthly_processor = MonthlyBackupProcessor(self.config_manager)
            result = monthly_processor.process_monthly_backup(
                month=month_num,
                year=year,
                table_names=table_names,
                dry_run=dry_run
            )
            
            return result
                
        except Exception as e:
            error_details = handle_error(e, "Enhanced monthly backup processing")
            return {
                'status': 'critical_failure',
                'error': str(e),
                'error_details': error_details,
                'duration': (datetime.datetime.now() - self.start_time).total_seconds()
            }
    
    def _perform_health_checks(self) -> Dict[str, Any]:
        """Perform comprehensive health checks."""
        logger.info("Performing system health checks...")

        try:
            health_results = self.health_checker.perform_comprehensive_health_check()

            # Convert to expected format
            is_healthy = health_results.get('overall_status') in ['HEALTHY', 'WARNING']

            # Log health check results
            if is_healthy:
                logger.info("Health check completed: ✅ HEALTHY")
            else:
                logger.warning("Health check completed: ❌ UNHEALTHY")
                for check in health_results.get('checks', []):
                    if check.get('status') == 'CRITICAL':
                        logger.warning(f"Health issue: {check.get('message', 'Unknown issue')}")

            return {
                'healthy': is_healthy,
                'details': health_results
            }

        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                'healthy': False,
                'issues': [f"Health check system failure: {str(e)}"]
            }
    
    def _parse_month(self, month_name: str) -> Optional[int]:
        """Parse month name or number to month number."""
        if month_name.isdigit():
            month_num = int(month_name)
            return month_num if 1 <= month_num <= 12 else None

        month_names = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        return month_names.get(month_name.lower())
    
    def _load_table_configuration(self) -> List[str]:
        """Load table configuration with fallbacks."""
        # Try to load from specified config path first
        if self.config_path:
            table_names = self._load_from_file(self.config_path)
            if table_names:
                return table_names
        
        # Try default paths
        default_paths = [
            'tabletest/tables.json',
            'config/monthly_tables.json',
            'config/tables.json'
        ]
        
        for path in default_paths:
            table_names = self._load_from_file(path)
            if table_names:
                logger.info(f"Loaded {len(table_names)} tables from {path}")
                return table_names
        
        # Fallback to default tables
        logger.warning("Could not load table configuration, using default tables")
        return ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
    
    def _load_from_file(self, file_path: str) -> Optional[List[str]]:
        """Load table names from a specific file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'tables' in data:
                return data['tables']
            else:
                logger.warning(f"Invalid table configuration format in {file_path}")
                return None
                
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            logger.debug(f"Could not load table configuration from {file_path}: {str(e)}")
            return None


def main():
    """Main entry point for enhanced monthly backup processor."""
    parser = argparse.ArgumentParser(description='Enhanced Monthly Backup Processor')
    parser.add_argument('--month', required=True, help='Month name or number')
    parser.add_argument('--year', type=int, required=True, help='Year')
    parser.add_argument('--config-path', help='Path to table configuration file')
    parser.add_argument('--dry-run', action='store_true', help='Validate only')
    parser.add_argument('--verbose', action='store_true', help='Verbose logging')
    parser.add_argument('--force-recovery', action='store_true', help='Force recovery mode')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = EnhancedMonthlyBackupProcessor(args.config_path)
    
    # Run backup
    result = processor.process_monthly_backup(
        args.month, args.year, args.dry_run, args.verbose, args.force_recovery
    )
    
    # Print results
    print(f"Backup completed with status: {result.get('status', 'unknown')}")
    if result.get('error'):
        print(f"Error: {result['error']}")
    
    # Exit with appropriate code
    sys.exit(0 if result.get('status') == 'completed' else 1)


if __name__ == '__main__':
    main()
