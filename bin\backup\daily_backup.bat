@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Daily Backup Module                             S4NG-7
REM ===============================================================
REM Focused daily backup functionality
REM - Sequential table processing
REM - Error handling with retry mechanism
REM - Performance monitoring
REM - Automatic cleanup
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM ===============================================================
REM INITIALIZATION
REM ===============================================================

:init_daily_backup
REM Initialize logging
call :get_timestamp timestamp
call :create_log_directory "daily" "%timestamp%" log_dir
set "LOG_FILE=%log_dir%\daily_backup_%timestamp%.log"
call :init_logging "%LOG_FILE%" "DailyBackup" %LOG_LEVEL_INFO%

call :log_operation_start "Daily Backup System"
call :log_system_info

REM Load configuration
call :load_default_config
call :find_table_config TABLE_CONFIG_PATH
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration not found"
    exit /b %ERROR_CONFIG%
)

call :validate_table_config "%TABLE_CONFIG_PATH%"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration validation failed"
    exit /b %ERROR_CONFIG%
)

REM Process command line arguments
call :process_backup_arguments %*
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

REM Validate configuration
call :validate_config_parameters
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Configuration parameter validation failed"
    exit /b %ERROR_CONFIG%
)

goto :main_backup_process

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_backup_arguments
REM Process command line arguments for backup mode
:parse_backup_args
if "%~1"=="" goto backup_args_done

if /i "%~1"=="--dry-run" (
    set DRY_RUN=true
    call :log_info "Dry run mode enabled"
    shift & goto parse_backup_args
)

if /i "%~1"=="--verbose" (
    set VERBOSE=true
    set CURRENT_LOG_LEVEL=%LOG_LEVEL_DEBUG%
    call :log_info "Verbose mode enabled"
    shift & goto parse_backup_args
)

if /i "%~1"=="--single-table" (
    set SINGLE_TABLE_MODE=true
    call :log_info "Single table mode enabled"
    shift & goto parse_backup_args
)

if /i "%~1"=="--force-email" (
    set FORCE_EMAIL=true
    call :log_info "Force email mode enabled"
    shift & goto parse_backup_args
)

if /i "%~1"=="--chunk-size" (
    if not "%~2"=="" (
        set CHUNK_SIZE=%~2
        call :log_info "Chunk size set to: %~2"
        shift & shift & goto parse_backup_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --chunk-size"
        exit /b %ERROR_INVALID_PARAM%
    )
)

if /i "%~1"=="--timeout" (
    if not "%~2"=="" (
        set TIMEOUT=%~2
        call :log_info "Timeout set to: %~2"
        shift & shift & goto parse_backup_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --timeout"
        exit /b %ERROR_INVALID_PARAM%
    )
)

REM Unknown parameter
call :log_warning "Unknown parameter: %~1"
shift & goto parse_backup_args

:backup_args_done
goto :eof

REM ===============================================================
REM MAIN BACKUP PROCESS
REM ===============================================================

:main_backup_process
call :log_info "Starting main backup process"
call :show_current_config

REM Pre-backup checks
call :pre_backup_checks
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

REM Count tables for progress tracking
call :count_json_tables "%TABLE_CONFIG_PATH%" total_tables
call :log_info "Found %total_tables% tables to process"

if %total_tables% EQU 0 (
    call :log_warning "No tables found in configuration"
    exit /b 0
)

REM Execute backup process
if "%DRY_RUN%"=="true" (
    call :execute_dry_run
    set backup_exit_code=!ERRORLEVEL!
) else (
    call :execute_backup
    set backup_exit_code=!ERRORLEVEL!
)

REM Post-backup cleanup
call :post_backup_cleanup

REM Generate summary
call :generate_backup_summary %backup_exit_code%

call :log_operation_end "Daily Backup System" %backup_exit_code%
exit /b %backup_exit_code%

REM ===============================================================
REM PRE-BACKUP CHECKS
REM ===============================================================

:pre_backup_checks
call :log_info "Performing pre-backup checks..."

REM Check Python availability
call :safe_execute "call :check_python_available" "Python availability check"
if %ERRORLEVEL% NEQ 0 exit /b %ERROR_PYTHON%

REM Check disk space
call :safe_execute "call :check_disk_space %MIN_DISK_SPACE_GB%" "Disk space check"
if %ERRORLEVEL% NEQ 0 (
    call :log_warning "Low disk space detected, attempting cleanup..."
    call :safe_execute "%PYTHON_CMD% utils\disk_cleanup.py --force --verbose" "Emergency disk cleanup"
    if !ERRORLEVEL! NEQ 0 (
        call :handle_error %ERROR_DISK_FULL% "Insufficient disk space for backup"
        exit /b %ERROR_DISK_FULL%
    )
)

REM Validate backup script exists
set BACKUP_SCRIPT=scripts\daily_backup_scheduler.py
call :validate_file_path "%BACKUP_SCRIPT%" "Daily backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Daily backup script not found: %BACKUP_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :log_info "Pre-backup checks completed successfully"
goto :eof

REM ===============================================================
REM BACKUP EXECUTION
REM ===============================================================

:execute_dry_run
call :log_info "Executing dry run validation..."

REM Build parameters for dry run
set "backup_params=--dry-run"
if "%VERBOSE%"=="true" set "backup_params=%backup_params% --verbose"
if "%SINGLE_TABLE_MODE%"=="true" set "backup_params=%backup_params% --single-table"

call :log_info "Running: %PYTHON_CMD% %BACKUP_SCRIPT% %backup_params%"

REM Execute with retry mechanism
call :safe_execute_with_retry "%PYTHON_CMD% %BACKUP_SCRIPT% %backup_params%" "Dry run execution" %RETRY_COUNT%
set dry_run_exit_code=!ERRORLEVEL!

if %dry_run_exit_code% EQU 0 (
    call :log_info "Dry run completed successfully"
) else (
    call :log_error "Dry run failed with exit code: %dry_run_exit_code%"
)

exit /b %dry_run_exit_code%

:execute_backup
call :log_info "Executing backup process..."

REM Build parameters for backup
set "backup_params="
if "%VERBOSE%"=="true" set "backup_params=%backup_params% --verbose"
if "%SINGLE_TABLE_MODE%"=="true" set "backup_params=%backup_params% --single-table"
if "%FORCE_EMAIL%"=="true" set "backup_params=%backup_params% --force-email"
if not "%CHUNK_SIZE%"=="%DEFAULT_CHUNK_SIZE%" set "backup_params=%backup_params% --chunk-size %CHUNK_SIZE%"
if not "%TIMEOUT%"=="%DEFAULT_TIMEOUT%" set "backup_params=%backup_params% --timeout %TIMEOUT%"

call :log_info "Running: %PYTHON_CMD% %BACKUP_SCRIPT% %backup_params%"

REM Execute backup with comprehensive error handling
call :safe_execute_with_retry "%PYTHON_CMD% %BACKUP_SCRIPT% %backup_params%" "Backup execution" %RETRY_COUNT%
set backup_exit_code=!ERRORLEVEL!

if %backup_exit_code% EQU 0 (
    call :log_info "Backup completed successfully"
) else (
    call :log_error "Backup failed with exit code: %backup_exit_code%"
    
    REM Attempt recovery if possible
    call :attempt_recovery %backup_exit_code%
    if !ERRORLEVEL! EQU 0 (
        call :log_info "Recovery successful, retrying backup..."
        call :safe_execute "%PYTHON_CMD% %BACKUP_SCRIPT% %backup_params%" "Backup retry after recovery"
        set backup_exit_code=!ERRORLEVEL!
    )
)

exit /b %backup_exit_code%

REM ===============================================================
REM POST-BACKUP OPERATIONS
REM ===============================================================

:post_backup_cleanup
call :log_info "Performing post-backup cleanup..."

REM Clean up temporary files
call :cleanup_temp_files "*.tmp"
call :cleanup_temp_files "backup_*.temp"

REM Run disk cleanup utility
if exist "utils\disk_cleanup.py" (
    call :safe_execute "%PYTHON_CMD% utils\disk_cleanup.py --force --verbose" "Post-backup disk cleanup"
)

REM Rotate logs if needed
call :rotate_logs "%log_dir%" 10

call :log_info "Post-backup cleanup completed"
goto :eof

:generate_backup_summary
setlocal
set "exit_code=%~1"

call :log_info "Generating backup summary..."

echo.
echo ===============================================================
echo DAILY BACKUP SUMMARY
echo ===============================================================
echo Completion Time: %date% %time%
echo Exit Code: %exit_code%
echo Configuration: %TABLE_CONFIG_PATH%
echo Log File: %LOG_FILE%

if "%exit_code%"=="0" (
    echo Status: ✅ SUCCESS
    echo All tables were processed successfully
) else if "%exit_code%"=="1" (
    echo Status: ⚠️ COMPLETED WITH WARNINGS
    echo Some tables may have encountered issues
) else (
    echo Status: ❌ FAILED
    echo Backup process encountered errors
)

echo.
echo Recent Log Entries:
echo ===============================================================
call :show_recent_logs 10
echo ===============================================================

goto :eof
