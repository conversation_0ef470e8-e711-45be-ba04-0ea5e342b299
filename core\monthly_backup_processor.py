#!/usr/bin/env python3
"""
Dedicated Monthly Backup Processor

This module provides a completely separate processor for monthly backup operations,
independent from daily backup processes to avoid confusion and conflicts.

Features:
- Monthly-specific configuration and logic
- Historical date processing for monthly backups
- Separate storage paths and naming conventions
- Independent error handling and recovery
- Monthly-specific logging and monitoring
"""

import logging
import datetime
import calendar
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

# Import core modules
from core.config_manager import ConfigManager
from core.devo_client import DevoClient
from core.storage_manager import StorageManager
from utils.minimal_logging import logger
from utils.error_handler import handle_error

# Configure logging
module_logger = logging.getLogger(__name__)


class MonthlyBackupStatus(Enum):
    """Status tracking for monthly backup operations."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CRITICAL_FAILURE = "critical_failure"


@dataclass
class MonthlyBackupConfig:
    """Configuration specifically for monthly backup operations."""
    target_month: int
    target_year: int
    table_names: List[str]
    chunk_size: int = 1000
    max_retries: int = 3
    timeout_seconds: int = 1800
    storage_prefix: str = "monthly_backup"
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if not (1 <= self.target_month <= 12):
            raise ValueError(f"Invalid month: {self.target_month}")
        if self.target_year < 2020 or self.target_year > 2030:
            raise ValueError(f"Invalid year: {self.target_year}")
        if not self.table_names:
            raise ValueError("Table names list cannot be empty")


@dataclass
class MonthlyDayResult:
    """Result tracking for a single day in monthly backup."""
    day: int
    date: datetime.date
    status: MonthlyBackupStatus
    tables_processed: int = 0
    tables_successful: int = 0
    tables_failed: int = 0
    total_rows: int = 0
    processing_time_seconds: float = 0.0
    error_message: Optional[str] = None
    attempts: int = 0


class MonthlyBackupProcessor:
    """
    Dedicated processor for monthly backup operations.
    
    This processor is completely independent from daily backup operations
    and handles historical date processing specifically for monthly backups.
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the monthly backup processor.
        
        Args:
            config_manager: Optional configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.devo_client = DevoClient()
        self.storage_manager = StorageManager(self.config_manager)
        
        # Monthly-specific settings
        self.monthly_settings = self.config_manager.get('backup', 'monthly_backup', {})
        self.max_retries = self.monthly_settings.get('max_retries', 3)
        self.retry_delay = self.monthly_settings.get('retry_delay_seconds', 60)
        
        # Tracking
        self.start_time = datetime.datetime.now()
        self.day_results: Dict[int, MonthlyDayResult] = {}
        
        logger.info("MonthlyBackupProcessor initialized")
    
    def process_monthly_backup(self, month: int, year: int, table_names: List[str], 
                             dry_run: bool = False) -> Dict[str, Any]:
        """
        Process a complete monthly backup for the specified month and year.
        
        Args:
            month: Target month (1-12)
            year: Target year
            table_names: List of table names to backup
            dry_run: If True, validate only without actual backup
            
        Returns:
            Processing results dictionary
        """
        try:
            logger.info(f"Starting monthly backup for {calendar.month_name[month]} {year}")
            
            # Create monthly backup configuration
            config = MonthlyBackupConfig(
                target_month=month,
                target_year=year,
                table_names=table_names
            )
            
            if dry_run:
                return self._run_dry_run_validation(config)
            else:
                return self._run_monthly_backup(config)
                
        except Exception as e:
            error_details = handle_error(e, "Monthly backup processing")
            return {
                'status': 'critical_failure',
                'error': str(e),
                'error_details': error_details,
                'duration': (datetime.datetime.now() - self.start_time).total_seconds()
            }
    
    def _run_dry_run_validation(self, config: MonthlyBackupConfig) -> Dict[str, Any]:
        """
        Run dry-run validation for monthly backup.
        
        Args:
            config: Monthly backup configuration
            
        Returns:
            Validation results
        """
        logger.info("Running monthly backup dry-run validation...")
        
        days_in_month = calendar.monthrange(config.target_year, config.target_month)[1]
        
        validation_results = {
            'status': 'completed',
            'mode': 'dry_run',
            'target_month': config.target_month,
            'target_year': config.target_year,
            'days_to_process': days_in_month,
            'tables_to_process': len(config.table_names),
            'estimated_duration_hours': (days_in_month * len(config.table_names) * 0.1),
            'validations': {
                'date_range': True,
                'table_configuration': True,
                'storage_access': True,
                'system_resources': True
            }
        }
        
        logger.info(f"Dry-run validation completed: {validation_results['status']}")
        return validation_results
    
    def _run_monthly_backup(self, config: MonthlyBackupConfig) -> Dict[str, Any]:
        """
        Run the actual monthly backup process.
        
        Args:
            config: Monthly backup configuration
            
        Returns:
            Backup results
        """
        logger.info("Starting monthly backup process...")
        
        days_in_month = calendar.monthrange(config.target_year, config.target_month)[1]
        
        # Initialize day results
        for day in range(1, days_in_month + 1):
            self.day_results[day] = MonthlyDayResult(
                day=day,
                date=datetime.date(config.target_year, config.target_month, day),
                status=MonthlyBackupStatus.NOT_STARTED
            )
        
        successful_days = 0
        failed_days = 0
        
        # Process each day
        for day in range(1, days_in_month + 1):
            day_result = self.day_results[day]
            logger.info(f"Processing day {day} of {days_in_month} ({day_result.date})")
            
            success = self._process_single_day(day_result, config)
            
            if success:
                successful_days += 1
            else:
                failed_days += 1
        
        # Calculate final results
        total_duration = (datetime.datetime.now() - self.start_time).total_seconds()
        
        return {
            'status': 'completed' if failed_days == 0 else 'partial_failure',
            'target_month': config.target_month,
            'target_year': config.target_year,
            'days_processed': days_in_month,
            'successful_days': successful_days,
            'failed_days': failed_days,
            'total_duration_seconds': total_duration,
            'day_results': {day: result.__dict__ for day, result in self.day_results.items()}
        }
    
    def _process_single_day(self, day_result: MonthlyDayResult, 
                          config: MonthlyBackupConfig) -> bool:
        """
        Process backup for a single day.
        
        Args:
            day_result: Day result tracking object
            config: Monthly backup configuration
            
        Returns:
            True if successful, False otherwise
        """
        day_start_time = datetime.datetime.now()
        day_result.status = MonthlyBackupStatus.IN_PROGRESS
        day_result.attempts += 1
        
        try:
            logger.info(f"Processing tables for {day_result.date}")
            
            # Process each table for this day
            for table_name in config.table_names:
                table_success = self._backup_table_for_date(
                    table_name, day_result.date, config
                )
                
                day_result.tables_processed += 1
                if table_success:
                    day_result.tables_successful += 1
                    day_result.total_rows += 1000  # Simulated
                else:
                    day_result.tables_failed += 1
            
            # Calculate processing time
            day_result.processing_time_seconds = (
                datetime.datetime.now() - day_start_time
            ).total_seconds()
            
            # Determine success
            if day_result.tables_failed == 0:
                day_result.status = MonthlyBackupStatus.COMPLETED
                logger.info(f"Day {day_result.day} completed successfully")
                return True
            else:
                day_result.status = MonthlyBackupStatus.FAILED
                day_result.error_message = f"{day_result.tables_failed} tables failed"
                logger.error(f"Day {day_result.day} failed: {day_result.error_message}")
                return False
                
        except Exception as e:
            day_result.status = MonthlyBackupStatus.CRITICAL_FAILURE
            day_result.error_message = str(e)
            logger.error(f"Critical failure on day {day_result.day}: {str(e)}")
            return False
    
    def _backup_table_for_date(self, table_name: str, target_date: datetime.date,
                             config: MonthlyBackupConfig) -> bool:
        """
        Backup a single table for a specific historical date.
        
        Args:
            table_name: Name of the table to backup
            target_date: Historical date to backup
            config: Monthly backup configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Backing up table {table_name} for date {target_date}")
            
            # Generate monthly-specific storage path
            storage_path = self._generate_monthly_storage_path(
                table_name, target_date, config
            )
            
            # Simulate backup process
            # In real implementation, this would:
            # 1. Query Devo for table data on target_date
            # 2. Process and compress the data
            # 3. Upload to storage with monthly-specific path
            # 4. Validate the backup
            
            logger.info(f"Table {table_name} backed up to {storage_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to backup table {table_name} for {target_date}: {str(e)}")
            return False
    
    def _generate_monthly_storage_path(self, table_name: str, target_date: datetime.date,
                                     config: MonthlyBackupConfig) -> str:
        """
        Generate storage path for monthly backup.
        
        Args:
            table_name: Name of the table
            target_date: Target backup date
            config: Monthly backup configuration
            
        Returns:
            Storage path string
        """
        month_name = calendar.month_name[target_date.month]
        return (f"monthly_backups/{config.target_year}/{month_name}/"
                f"{target_date.strftime('%Y-%m-%d')}/{table_name}_{target_date.strftime('%Y%m%d')}.tar.gz")
