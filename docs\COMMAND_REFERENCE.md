# TNGD Backup System - Complete Command Reference

## 📋 **OVERVIEW**

This document provides a comprehensive reference for all available commands, modes, and options in the TNGD backup automation system. The system provides both Windows batch scripts and direct Python script execution.

---

## 🚀 **MAIN BACKUP COMMANDS**

### **1. Daily Backup System**

#### **Windows Batch Script**
```bash
bin\run_daily_backup.bat [mode] [options]
```

#### **Direct Python Execution**
```bash
python scripts/daily_backup_scheduler.py [options]
```

---

### **2. Monthly Backup System (Enhanced)**

#### **Windows Batch Script**
```bash
bin\run_monthly_backup_enhanced.bat <month> <year> [options]
```

#### **Direct Python Execution**
```bash
python scripts/enhanced_monthly_backup_processor.py --month <month> --year <year> [options]
```

---

## 📖 **DETAILED COMMAND REFERENCE**

### **🔹 DAILY BACKUP COMMANDS**

#### **A. Basic Daily Backup**
```bash
# Default daily backup (all tables)
bin\run_daily_backup.bat
bin\run_daily_backup.bat backup

# Direct Python
python scripts/daily_backup_scheduler.py
```

#### **B. Daily Backup Modes**

| **Mode** | **Command** | **Description** |
|----------|-------------|-----------------|
| `backup` | `bin\run_daily_backup.bat backup` | Run daily backup (default) |
| `single-table` | `bin\run_daily_backup.bat single-table <table>` | Backup single table |
| `test` | `bin\run_daily_backup.bat test [test-type]` | Run system tests |
| `setup` | `bin\run_daily_backup.bat setup [command]` | Setup task scheduler |
| `help` | `bin\run_daily_backup.bat help` | Show help message |

#### **C. Daily Backup Options**

| **Option** | **Type** | **Description** | **Example** |
|------------|----------|-----------------|-------------|
| `--dry-run` | Flag | Validate tables only, no backup | `--dry-run` |
| `--single-table` | Flag | Process only one table for testing | `--single-table` |
| `--force-email` | Flag | Send email even in test mode | `--force-email` |
| `--verbose` | Flag | Enable verbose logging | `--verbose` |
| `--chunk-size` | Integer | Override default chunk size | `--chunk-size 50000` |
| `--timeout` | Integer | Override default timeout | `--timeout 1800` |
| `--date` | String | Backup for specific date (YYYY-MM-DD) | `--date 2025-06-20` |
| `--days` | Integer | Number of days to backup | `--days 7` |

#### **D. Daily Backup Examples**
```bash
# Basic daily backup
bin\run_daily_backup.bat

# Dry run validation
bin\run_daily_backup.bat --dry-run

# Verbose backup with custom chunk size
bin\run_daily_backup.bat --verbose --chunk-size 50000

# Single table backup
bin\run_daily_backup.bat single-table my.app.tngd.waf

# Single table with options
bin\run_daily_backup.bat single-table my.app.tngd.waf --days 7 --dry-run

# Backup for specific date
python scripts/daily_backup_scheduler.py --date 2025-06-20

# Single table testing
python scripts/daily_backup_scheduler.py --single-table --verbose
```

---

### **🔹 SINGLE TABLE MODE**

#### **A. Single Table Commands**
```bash
# Basic single table backup
bin\run_daily_backup.bat single-table <table_name>

# List all available tables
bin\run_daily_backup.bat single-table --list-tables
```

#### **B. Single Table Options**

| **Option** | **Type** | **Description** | **Example** |
|------------|----------|-----------------|-------------|
| `--list-tables` | Flag | List all available tables | `--list-tables` |
| `--dry-run` | Flag | Test mode - validate only | `--dry-run` |
| `--days` | Integer | Number of days to backup (default: 1) | `--days 7` |
| `--chunk-size` | Integer | Chunk size for processing | `--chunk-size 100000` |
| `--verbose` | Flag | Enable detailed logging | `--verbose` |

#### **C. Single Table Examples**
```bash
# Backup specific table
bin\run_daily_backup.bat single-table my.app.tngd.waf

# List available tables
bin\run_daily_backup.bat single-table --list-tables

# Multi-day single table backup
bin\run_daily_backup.bat single-table my.app.tngd.waf --days 7

# Single table dry run
bin\run_daily_backup.bat single-table my.app.tngd.waf --dry-run --verbose
```

---

### **🔹 TEST MODE**

#### **A. Test Commands**
```bash
bin\run_daily_backup.bat test [test-type] [options]
```

#### **B. Test Types**

| **Test Type** | **Description** | **Example** |
|---------------|-----------------|-------------|
| `dry-run` | Validate tables only | `bin\run_daily_backup.bat test dry-run` |
| `single-table` | Test single table backup | `bin\run_daily_backup.bat test single-table` |
| `full-test` | Test complete backup process | `bin\run_daily_backup.bat test full-test` |
| `performance` | Run performance benchmarks | `bin\run_daily_backup.bat test performance` |
| `error-handling` | Test error scenarios | `bin\run_daily_backup.bat test error-handling` |
| `all-tests` | Run comprehensive test suite | `bin\run_daily_backup.bat test all-tests` |
| `quick` | Run quick test suite | `bin\run_daily_backup.bat test quick` |
| `help` | Show test help | `bin\run_daily_backup.bat test help` |

#### **C. Test Examples**
```bash
# Quick validation
bin\run_daily_backup.bat test dry-run

# Single table test
bin\run_daily_backup.bat test single-table

# Full test suite
bin\run_daily_backup.bat test all-tests

# Performance testing
bin\run_daily_backup.bat test performance
```

---

### **🔹 SETUP MODE (Task Scheduler)**

#### **A. Setup Commands**
```bash
bin\run_daily_backup.bat setup [command] [options]
```

#### **B. Setup Commands**

| **Command** | **Description** | **Example** |
|-------------|-----------------|-------------|
| `create-daily` | Create daily backup task | `bin\run_daily_backup.bat setup create-daily` |
| `create-weekly` | Create weekly backup task | `bin\run_daily_backup.bat setup create-weekly` |
| `create-monthly` | Create monthly backup task | `bin\run_daily_backup.bat setup create-monthly` |
| `create-all` | Create all backup tasks | `bin\run_daily_backup.bat setup create-all` |
| `list` | List all TNGD backup tasks | `bin\run_daily_backup.bat setup list` |
| `status` | Show status of backup tasks | `bin\run_daily_backup.bat setup status` |
| `delete` | Delete backup tasks | `bin\run_daily_backup.bat setup delete` |
| `help` | Show setup help | `bin\run_daily_backup.bat setup help` |

#### **C. Setup Examples**
```bash
# Create daily backup task
bin\run_daily_backup.bat setup create-daily --time 02:30

# List all backup tasks
bin\run_daily_backup.bat setup list

# Check task status
bin\run_daily_backup.bat setup status

# Delete specific task
bin\run_daily_backup.bat setup delete daily-backup
```

---

### **🔹 MONTHLY BACKUP COMMANDS (Enhanced)**

#### **A. Basic Monthly Backup**
```bash
# Basic monthly backup
bin\run_monthly_backup_enhanced.bat <month> <year>

# Direct Python
python scripts/enhanced_monthly_backup_processor.py --month <month> --year <year>
```

#### **B. Monthly Backup Options**

| **Option** | **Type** | **Description** | **Example** |
|------------|----------|-----------------|-------------|
| `--dry-run` | Flag | Validate only, no backup | `--dry-run` |
| `--verbose` | Flag | Enable verbose logging | `--verbose` |
| `--force-recovery` | Flag | Force recovery mode | `--force-recovery` |
| `--skip-health-checks` | Flag | Skip system health checks | `--skip-health-checks` |
| `--config-path` | String | Path to table configuration file | `--config-path config/tables.json` |
| `help` / `--help` | Flag | Show help message | `help` |

#### **C. Monthly Backup Examples**
```bash
# Basic monthly backup
bin\run_monthly_backup_enhanced.bat march 2025

# Dry run validation
bin\run_monthly_backup_enhanced.bat march 2025 --dry-run

# Verbose monitoring
bin\run_monthly_backup_enhanced.bat march 2025 --verbose

# Force recovery mode
bin\run_monthly_backup_enhanced.bat march 2025 --force-recovery

# Skip health checks (not recommended)
bin\run_monthly_backup_enhanced.bat march 2025 --skip-health-checks

# Custom configuration
bin\run_monthly_backup_enhanced.bat march 2025 --config-path backup/tables.json

# Combined options
bin\run_monthly_backup_enhanced.bat march 2025 --dry-run --verbose --force-recovery
```

---

## 🛠️ **UTILITY COMMANDS**

### **🔹 System Health Checker**

#### **A. Basic Health Check**
```bash
python utils/system_health_checker.py [options]
```

#### **B. Health Check Options**

| **Option** | **Description** | **Example** |
|------------|-----------------|-------------|
| `--json` | Output results as JSON | `--json` |
| `--verbose` | Verbose output | `--verbose` |

#### **C. Health Check Examples**
```bash
# Basic health check
python utils/system_health_checker.py

# JSON output for automation
python utils/system_health_checker.py --json

# Verbose health report
python utils/system_health_checker.py --verbose
```

---

### **🔹 Configuration Validator**

#### **A. Basic Configuration Validation**
```bash
python utils/config_validator.py [options]
```

#### **B. Configuration Validator Options**

| **Option** | **Description** | **Example** |
|------------|-----------------|-------------|
| `--json` | Output results as JSON | `--json` |
| `--verbose` | Verbose output | `--verbose` |

#### **C. Configuration Validator Examples**
```bash
# Basic configuration validation
python utils/config_validator.py

# JSON output for CI/CD
python utils/config_validator.py --json

# Detailed validation report
python utils/config_validator.py --verbose
```

---

### **🔹 Disk Cleanup Utility**

#### **A. Basic Disk Cleanup**
```bash
python utils/disk_cleanup.py [options]
```

#### **B. Disk Cleanup Options**

| **Option** | **Description** | **Example** |
|------------|-----------------|-------------|
| `--force` | Force cleanup regardless of disk usage | `--force` |
| `--verbose` | Enable verbose logging | `--verbose` |
| `--emergency` | Perform emergency cleanup | `--emergency` |

#### **C. Disk Cleanup Examples**
```bash
# Basic cleanup (only if needed)
python utils/disk_cleanup.py

# Force cleanup
python utils/disk_cleanup.py --force

# Emergency cleanup
python utils/disk_cleanup.py --emergency

# Verbose cleanup
python utils/disk_cleanup.py --verbose --force
```

---

## 📊 **COMMAND SUMMARY TABLE**

### **Main Commands**

| **Command** | **Purpose** | **Key Options** |
|-------------|-------------|-----------------|
| `bin\run_daily_backup.bat` | Daily backup automation | `--dry-run`, `--verbose`, `--single-table` |
| `bin\run_monthly_backup_enhanced.bat` | Monthly backup automation | `--dry-run`, `--verbose`, `--force-recovery` |
| `python scripts/daily_backup_scheduler.py` | Direct daily backup | `--dry-run`, `--date`, `--chunk-size` |
| `python scripts/enhanced_monthly_backup_processor.py` | Direct monthly backup | `--month`, `--year`, `--dry-run` |

### **Utility Commands**

| **Command** | **Purpose** | **Key Options** |
|-------------|-------------|-----------------|
| `python utils/system_health_checker.py` | System health monitoring | `--json`, `--verbose` |
| `python utils/config_validator.py` | Configuration validation | `--json`, `--verbose` |
| `python utils/disk_cleanup.py` | Disk space management | `--force`, `--emergency` |

---

## 🎯 **QUICK REFERENCE**

### **Most Common Commands**
```bash
# Daily backup
bin\run_daily_backup.bat

# Monthly backup
bin\run_monthly_backup_enhanced.bat march 2025

# System health check
python utils/system_health_checker.py

# Configuration validation
python utils/config_validator.py

# Dry run testing
bin\run_daily_backup.bat --dry-run
bin\run_monthly_backup_enhanced.bat march 2025 --dry-run
```

### **Troubleshooting Commands**
```bash
# Verbose logging
bin\run_daily_backup.bat --verbose
bin\run_monthly_backup_enhanced.bat march 2025 --verbose

# Force recovery
bin\run_monthly_backup_enhanced.bat march 2025 --force-recovery

# Emergency cleanup
python utils/disk_cleanup.py --emergency

# Health check with details
python utils/system_health_checker.py --verbose
```

---

## 🔧 **ADVANCED OPTIONS & HIDDEN PARAMETERS**

### **🔹 Daily Backup Scheduler - Advanced Options**

#### **Hidden Parameters (for advanced users)**
These parameters are available but not shown in help (used internally):

| **Parameter** | **Type** | **Description** | **Example** |
|---------------|----------|-----------------|-------------|
| `--max-retries` | Integer | Override maximum retry attempts | `--max-retries 5` |
| `--batch-size` | Integer | Override batch processing size | `--batch-size 1000` |
| `--table-timeout` | Integer | Override per-table timeout | `--table-timeout 3600` |
| `--tables` | String | Specify custom table list | `--tables "table1,table2"` |
| `--mode` | String | Internal processing mode | `--mode daily` |
| `--month` | Integer | Month for monthly processing | `--month 3` |
| `--year` | Integer | Year for processing | `--year 2025` |
| `--chunking-strategy` | String | Data chunking strategy | `--chunking-strategy day` |
| `--monthly-max-retries` | Integer | Monthly backup max retries | `--monthly-max-retries 3` |
| `--monthly-retry-delay` | Integer | Monthly backup retry delay | `--monthly-retry-delay 30` |

#### **Advanced Daily Backup Examples**
```bash
# Advanced daily backup with custom parameters
python scripts/daily_backup_scheduler.py \
  --verbose \
  --chunk-size 75000 \
  --max-retries 5 \
  --timeout 2400 \
  --batch-size 500

# Specific table with advanced options
python scripts/daily_backup_scheduler.py my.app.tngd.waf \
  --days 3 \
  --chunk-size 50000 \
  --table-timeout 1800 \
  --verbose

# Custom date range processing
python scripts/daily_backup_scheduler.py \
  --date 2025-06-20 \
  --chunk-size 100000 \
  --max-retries 3 \
  --force-email
```

---

## 📋 **CONFIGURATION & ENVIRONMENT**

### **🔹 Environment Variables**

The system uses several environment variables for configuration:

| **Variable** | **Description** | **Default** |
|--------------|-----------------|-------------|
| `PYTHON_CMD` | Python command to use | `python` |
| `DEVO_API_KEY` | Devo API authentication key | *(required)* |
| `DEVO_API_SECRET` | Devo API secret | *(required)* |
| `SMTP_SERVER` | Email server for notifications | *(optional)* |
| `SMTP_PORT` | Email server port | `587` |
| `SMTP_USERNAME` | Email username | *(optional)* |
| `SMTP_PASSWORD` | Email password | *(optional)* |

### **🔹 Configuration Files**

| **File** | **Purpose** | **Location** |
|----------|-------------|--------------|
| `config.json` | Main system configuration | Root directory |
| `tabletest/tables.json` | Primary table definitions | `tabletest/` |
| `config/tables.json` | Fallback table definitions | `config/` |
| `backup/tables.json` | Secondary fallback tables | `backup/` |

---

## 🚨 **ERROR HANDLING & RECOVERY**

### **🔹 Common Error Scenarios**

#### **A. System Resource Issues**
```bash
# Check system health first
python utils/system_health_checker.py --verbose

# Force recovery if resources are constrained
bin\run_monthly_backup_enhanced.bat march 2025 --force-recovery

# Emergency disk cleanup
python utils/disk_cleanup.py --emergency
```

#### **B. Configuration Issues**
```bash
# Validate configuration
python utils/config_validator.py --verbose

# Use fallback configuration
bin\run_monthly_backup_enhanced.bat march 2025 --config-path backup/tables.json

# Skip health checks if configuration is known good
bin\run_monthly_backup_enhanced.bat march 2025 --skip-health-checks
```

#### **C. Network/Connectivity Issues**
```bash
# Test with single table first
bin\run_daily_backup.bat single-table my.app.tngd.waf --dry-run

# Increase timeout for slow connections
python scripts/daily_backup_scheduler.py --timeout 3600

# Use smaller chunk sizes for unstable connections
python scripts/daily_backup_scheduler.py --chunk-size 25000
```

---

## 📈 **MONITORING & AUTOMATION**

### **🔹 Automated Health Monitoring**
```bash
# JSON output for monitoring systems
python utils/system_health_checker.py --json > health_status.json

# Configuration validation for CI/CD
python utils/config_validator.py --json > config_status.json

# Automated cleanup
python utils/disk_cleanup.py --force > cleanup_log.txt
```

### **🔹 Scheduled Automation Examples**

#### **Windows Task Scheduler Integration**
```bash
# Create daily backup task
bin\run_daily_backup.bat setup create-daily --time 02:30

# Create weekly backup task
bin\run_daily_backup.bat setup create-weekly --time 01:00

# Create monthly backup task
bin\run_daily_backup.bat setup create-monthly --time 00:30
```

#### **PowerShell Automation Script Example**
```powershell
# Example PowerShell automation script
$healthCheck = python utils/system_health_checker.py --json | ConvertFrom-Json
if ($healthCheck.healthy) {
    bin\run_daily_backup.bat --verbose
} else {
    Write-Host "Health check failed, skipping backup"
    exit 1
}
```

---

## 🎯 **BEST PRACTICES**

### **🔹 Production Usage**
```bash
# Always run health check first
python utils/system_health_checker.py

# Use dry-run for validation
bin\run_daily_backup.bat --dry-run

# Enable verbose logging for troubleshooting
bin\run_daily_backup.bat --verbose

# Use force-recovery only when necessary
bin\run_monthly_backup_enhanced.bat march 2025 --force-recovery
```

### **🔹 Testing & Development**
```bash
# Quick validation
bin\run_daily_backup.bat test dry-run

# Single table testing
bin\run_daily_backup.bat single-table my.app.tngd.waf --dry-run

# Performance testing
bin\run_daily_backup.bat test performance

# Configuration testing
python utils/config_validator.py --verbose
```

### **🔹 Maintenance**
```bash
# Regular cleanup
python utils/disk_cleanup.py

# Emergency cleanup when disk is full
python utils/disk_cleanup.py --emergency

# Configuration validation
python utils/config_validator.py

# System health monitoring
python utils/system_health_checker.py --verbose
```

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **🔹 Log Locations**
- **Daily Logs**: `logs/daily/YYYY-MM-DD/`
- **Monthly Logs**: `logs/monthly_enhanced/YYYY-MM-DD/`
- **System Logs**: `logs/system/`
- **Error Logs**: `logs/backup.log`

### **🔹 Common Issues & Solutions**

| **Issue** | **Command** | **Solution** |
|-----------|-------------|--------------|
| High CPU/Memory | `python utils/system_health_checker.py` | Wait or use `--force-recovery` |
| Configuration Error | `python utils/config_validator.py --verbose` | Fix configuration issues |
| Disk Space Low | `python utils/disk_cleanup.py --emergency` | Free up disk space |
| Network Timeout | `--timeout 3600 --chunk-size 25000` | Increase timeout, reduce chunk size |
| Table Access Error | `--dry-run` first | Validate table access |

This comprehensive command reference covers all available commands, modes, options, and advanced usage patterns in the TNGD backup automation system! 🚀
