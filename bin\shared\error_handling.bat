@echo off
REM ===============================================================
REM TNGD Error Handling Library                          S4NG-7
REM ===============================================================
REM Standardized error handling for TNGD backup system
REM - Consistent error reporting
REM - Exit code management
REM - Error recovery mechanisms
REM - Retry logic with exponential backoff
REM ===============================================================

REM Load common functions if not already loaded
if not defined TNGD_COMMON_LOADED call "%~dp0common_functions.bat"

REM ===============================================================
REM ERROR CODE DEFINITIONS
REM ===============================================================

REM Standard exit codes
set ERROR_SUCCESS=0
set ERROR_GENERAL=1
set ERROR_INVALID_PARAM=2
set ERROR_FILE_NOT_FOUND=3
set ERROR_ACCESS_DENIED=4
set ERROR_DISK_FULL=5
set ERROR_NETWORK=6
set ERROR_TIMEOUT=7
set ERROR_CONFIG=8
set ERROR_PYTHON=9
set ERROR_BACKUP_FAILED=10

REM ===============================================================
REM CORE ERROR HANDLING FUNCTIONS
REM ===============================================================

:handle_error
REM Usage: call :handle_error error_code "error_message" ["context"]
setlocal enabledelayedexpansion
set "error_code=%~1"
set "error_message=%~2"
set "context=%~3"

if "%error_code%"=="" set "error_code=%ERROR_GENERAL%"
if "%error_message%"=="" set "error_message=Unknown error occurred"

REM Format error message
set "full_message=%error_message%"
if not "%context%"=="" set "full_message=%error_message% (Context: %context%)"

REM Log the error
call :log_error "ERROR_%error_code%: %full_message%"

REM Display user-friendly error message
call :display_error_message %error_code% "%full_message%"

REM Set global error state
set "LAST_ERROR_CODE=%error_code%"
set "LAST_ERROR_MESSAGE=%full_message%"

exit /b %error_code%

:display_error_message
REM Usage: call :display_error_message error_code "message"
setlocal
set "error_code=%~1"
set "message=%~2"

echo.
echo ===============================================================
echo ❌ ERROR OCCURRED
echo ===============================================================
echo Error Code: %error_code%
echo Message: %message%
echo Time: %date% %time%

REM Provide specific guidance based on error code
if "%error_code%"=="%ERROR_INVALID_PARAM%" (
    echo.
    echo 💡 Troubleshooting:
    echo   • Check command line parameters
    echo   • Verify parameter format and values
    echo   • Use --help for usage information
)

if "%error_code%"=="%ERROR_FILE_NOT_FOUND%" (
    echo.
    echo 💡 Troubleshooting:
    echo   • Verify file paths are correct
    echo   • Check if files have been moved or deleted
    echo   • Ensure proper permissions to access files
)

if "%error_code%"=="%ERROR_CONFIG%" (
    echo.
    echo 💡 Troubleshooting:
    echo   • Check configuration file format
    echo   • Verify all required settings are present
    echo   • Review configuration file permissions
)

if "%error_code%"=="%ERROR_PYTHON%" (
    echo.
    echo 💡 Troubleshooting:
    echo   • Ensure Python is installed and in PATH
    echo   • Check Python version compatibility
    echo   • Verify required Python packages are installed
)

if "%error_code%"=="%ERROR_BACKUP_FAILED%" (
    echo.
    echo 💡 Troubleshooting:
    echo   • Check database connectivity
    echo   • Verify OSS credentials and permissions
    echo   • Review disk space availability
    echo   • Check network connectivity
)

echo ===============================================================
goto :eof

REM ===============================================================
REM RETRY MECHANISM WITH EXPONENTIAL BACKOFF
REM ===============================================================

:retry_with_backoff
REM Usage: call :retry_with_backoff "command" max_attempts base_delay_seconds
setlocal enabledelayedexpansion
set "command=%~1"
set "max_attempts=%~2"
set "base_delay=%~3"

if "%max_attempts%"=="" set "max_attempts=3"
if "%base_delay%"=="" set "base_delay=2"

set "attempt=1"
set "delay=%base_delay%"

:retry_loop
call :log_info "Attempt %attempt%/%max_attempts%: %command%"

REM Execute the command
%command%
set "exit_code=!ERRORLEVEL!"

if !exit_code! EQU 0 (
    call :log_info "Command succeeded on attempt %attempt%"
    exit /b 0
)

if !attempt! GEQ %max_attempts% (
    call :log_error "Command failed after %max_attempts% attempts"
    exit /b !exit_code!
)

call :log_warning "Attempt %attempt% failed (exit code: !exit_code!), retrying in %delay% seconds..."
timeout /t %delay% /nobreak >nul 2>&1

REM Exponential backoff: double the delay for next attempt
set /a "delay=delay*2"
set /a "attempt=attempt+1"
goto retry_loop

REM ===============================================================
REM ERROR RECOVERY FUNCTIONS
REM ===============================================================

:attempt_recovery
REM Usage: call :attempt_recovery error_code
setlocal
set "error_code=%~1"

call :log_info "Attempting automatic recovery for error code: %error_code%"

if "%error_code%"=="%ERROR_DISK_FULL%" (
    call :recover_disk_space
    exit /b !ERRORLEVEL!
)

if "%error_code%"=="%ERROR_CONFIG%" (
    call :recover_config
    exit /b !ERRORLEVEL!
)

if "%error_code%"=="%ERROR_NETWORK%" (
    call :recover_network
    exit /b !ERRORLEVEL!
)

call :log_warning "No automatic recovery available for error code: %error_code%"
exit /b 1

:recover_disk_space
call :log_info "Attempting disk space recovery..."
if exist "%TNGD_ROOT%\utils\disk_cleanup.py" (
    %PYTHON_CMD% "%TNGD_ROOT%\utils\disk_cleanup.py" --emergency --verbose
    if !ERRORLEVEL! EQU 0 (
        call :log_info "Disk space recovery successful"
        exit /b 0
    )
)
call :log_error "Disk space recovery failed"
exit /b 1

:recover_config
call :log_info "Attempting configuration recovery..."
REM Try to find alternative configuration files
if exist "%TNGD_ROOT%\config\tables.json" (
    call :log_info "Found alternative configuration file"
    exit /b 0
)
if exist "%TNGD_ROOT%\backup\tables.json" (
    call :log_info "Found backup configuration file"
    exit /b 0
)
call :log_error "Configuration recovery failed"
exit /b 1

:recover_network
call :log_info "Attempting network recovery..."
REM Simple network connectivity test
ping -n 1 8.8.8.8 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    call :log_info "Network connectivity restored"
    exit /b 0
)
call :log_error "Network recovery failed"
exit /b 1

REM ===============================================================
REM VALIDATION WITH ERROR HANDLING
REM ===============================================================

:validate_with_error
REM Usage: call :validate_with_error "validation_function" "param1" "param2" ...
setlocal
set "validation_function=%~1"
set "param1=%~2"
set "param2=%~3"
set "param3=%~4"

call :%validation_function% "%param1%" "%param2%" "%param3%"
set "validation_result=!ERRORLEVEL!"

if !validation_result! NEQ 0 (
    call :handle_error %ERROR_INVALID_PARAM% "Validation failed: %validation_function%" "%param1%"
    exit /b !validation_result!
)

exit /b 0

REM ===============================================================
REM SAFE COMMAND EXECUTION
REM ===============================================================

:safe_execute
REM Usage: call :safe_execute "command" "error_context"
setlocal enabledelayedexpansion
set "command=%~1"
set "error_context=%~2"

call :log_info "Executing: %command%"

%command%
set "exit_code=!ERRORLEVEL!"

if !exit_code! NEQ 0 (
    call :handle_error !exit_code! "Command execution failed" "%error_context%: %command%"
    exit /b !exit_code!
)

call :log_info "Command executed successfully"
exit /b 0

:safe_execute_with_retry
REM Usage: call :safe_execute_with_retry "command" "error_context" max_attempts
setlocal
set "command=%~1"
set "error_context=%~2"
set "max_attempts=%~3"

if "%max_attempts%"=="" set "max_attempts=3"

call :retry_with_backoff "%command%" %max_attempts% 2
set "exit_code=!ERRORLEVEL!"

if !exit_code! NEQ 0 (
    call :handle_error !exit_code! "Command failed after retries" "%error_context%: %command%"
    exit /b !exit_code!
)

exit /b 0

REM ===============================================================
REM ERROR REPORTING
REM ===============================================================

:generate_error_report
REM Usage: call :generate_error_report "report_file"
setlocal enabledelayedexpansion
set "report_file=%~1"

if "%report_file%"=="" (
    call :get_timestamp timestamp
    set "report_file=%TNGD_ROOT%\logs\error_report_!timestamp!.txt"
)

call :log_info "Generating error report: %report_file%"

echo TNGD Backup System Error Report > "%report_file%"
echo Generated: %date% %time% >> "%report_file%"
echo ======================================== >> "%report_file%"
echo. >> "%report_file%"

if defined LAST_ERROR_CODE (
    echo Last Error Code: %LAST_ERROR_CODE% >> "%report_file%"
    echo Last Error Message: %LAST_ERROR_MESSAGE% >> "%report_file%"
    echo. >> "%report_file%"
)

echo System Information: >> "%report_file%"
echo - OS: %OS% >> "%report_file%"
echo - Computer: %COMPUTERNAME% >> "%report_file%"
echo - User: %USERNAME% >> "%report_file%"
echo - Working Directory: %CD% >> "%report_file%"
echo. >> "%report_file%"

if exist "%LOG_FILE%" (
    echo Recent Log Entries: >> "%report_file%"
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 20" >> "%report_file%" 2>nul
)

call :log_info "Error report generated: %report_file%"
goto :eof
