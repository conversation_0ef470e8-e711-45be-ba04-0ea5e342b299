@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Test Runner Module                              S4NG-7
REM ===============================================================
REM Comprehensive testing framework for TNGD backup system
REM - Multiple test types and scenarios
REM - Automated test execution
REM - Detailed test reporting
REM - Performance benchmarking
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM ===============================================================
REM INITIALIZATION
REM ===============================================================

:init_test_runner
REM Initialize logging
call :get_timestamp timestamp
call :create_log_directory "tests" "%timestamp%" log_dir
set "LOG_FILE=%log_dir%\test_run_%timestamp%.log"
call :init_logging "%LOG_FILE%" "TestRunner" %LOG_LEVEL_INFO%

call :log_operation_start "TNGD Test Framework"

REM Load configuration
call :load_default_config

REM Process command line arguments
call :process_test_arguments %*
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

goto :main_test_process

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_test_arguments
set "TEST_TYPE="
set "TABLE_LIMIT=5"

REM Check if any arguments provided
if "%~1"=="" set "TEST_TYPE=help"

:parse_test_args
if "%~1"=="" goto test_args_done

if /i "%~1"=="help" (
    set TEST_TYPE=help
    shift & goto parse_test_args
)

if /i "%~1"=="dry-run" (
    set TEST_TYPE=dry-run
    shift & goto parse_test_args
)

if /i "%~1"=="single-table" (
    set TEST_TYPE=single-table
    shift & goto parse_test_args
)

if /i "%~1"=="full-test" (
    set TEST_TYPE=full-test
    if not "%~2"=="" (
        call :validate_safe_string "%~2" "table limit"
        if !ERRORLEVEL! EQU 0 (
            set TABLE_LIMIT=%~2
            shift
        )
    )
    shift & goto parse_test_args
)

if /i "%~1"=="performance" (
    set TEST_TYPE=performance
    shift & goto parse_test_args
)

if /i "%~1"=="error-handling" (
    set TEST_TYPE=error-handling
    shift & goto parse_test_args
)

if /i "%~1"=="all-tests" (
    set TEST_TYPE=all-tests
    shift & goto parse_test_args
)

if /i "%~1"=="quick" (
    set TEST_TYPE=quick
    shift & goto parse_test_args
)

if /i "%~1"=="--verbose" (
    set VERBOSE=true
    set CURRENT_LOG_LEVEL=%LOG_LEVEL_DEBUG%
    call :log_info "Verbose mode enabled"
    shift & goto parse_test_args
)

REM Unknown parameter
call :log_warning "Unknown test parameter: %~1"
shift & goto parse_test_args

:test_args_done
goto :eof

REM ===============================================================
REM MAIN TEST PROCESS
REM ===============================================================

:main_test_process
call :log_info "Starting test framework with test type: %TEST_TYPE%"

echo ===============================================================
echo TNGD BACKUP SYSTEM TESTING FRAMEWORK
echo ===============================================================
echo Test Type: %TEST_TYPE%
echo Log File: %LOG_FILE%
echo ===============================================================
echo.

REM Route to appropriate test function
if "%TEST_TYPE%"=="help" goto show_test_help
if "%TEST_TYPE%"=="dry-run" goto run_dry_run_test
if "%TEST_TYPE%"=="single-table" goto run_single_table_test
if "%TEST_TYPE%"=="full-test" goto run_full_test
if "%TEST_TYPE%"=="performance" goto run_performance_test
if "%TEST_TYPE%"=="error-handling" goto run_error_handling_test
if "%TEST_TYPE%"=="all-tests" goto run_all_tests
if "%TEST_TYPE%"=="quick" goto run_quick_test

REM Unknown test type
call :handle_error %ERROR_INVALID_PARAM% "Unknown test type: %TEST_TYPE%"
goto show_test_help

REM ===============================================================
REM INDIVIDUAL TEST IMPLEMENTATIONS
REM ===============================================================

:run_dry_run_test
call :log_info "Running dry-run validation test..."
echo Running dry-run validation test...

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --dry-run --verbose" "Dry-run test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Dry-run validation test" %test_exit_code%
call :log_operation_end "Dry-run Test" %test_exit_code%
exit /b %test_exit_code%

:run_single_table_test
call :log_info "Running single table backup test..."
echo Running single table backup test...

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --single-table --verbose" "Single table test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Single table backup test" %test_exit_code%
call :log_operation_end "Single Table Test" %test_exit_code%
exit /b %test_exit_code%

:run_full_test
call :log_info "Running full system backup test with table limit: %TABLE_LIMIT%"
echo Running full system backup test (limited to %TABLE_LIMIT% tables)...

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --full-test --table-limit %TABLE_LIMIT% --verbose" "Full system test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Full system backup test" %test_exit_code%
call :log_operation_end "Full System Test" %test_exit_code%
exit /b %test_exit_code%

:run_performance_test
call :log_info "Running performance benchmark test..."
echo Running performance benchmark test...

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --performance-test --verbose" "Performance test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Performance benchmark test" %test_exit_code%
call :log_operation_end "Performance Test" %test_exit_code%
exit /b %test_exit_code%

:run_error_handling_test
call :log_info "Running error handling test..."
echo Running error handling test...

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --error-test --verbose" "Error handling test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Error handling test" %test_exit_code%
call :log_operation_end "Error Handling Test" %test_exit_code%
exit /b %test_exit_code%

:run_all_tests
call :log_info "Running comprehensive test suite..."
echo Running comprehensive test suite...
echo WARNING: This may take 15-30 minutes to complete.

REM Confirm with user
set /p CONFIRM="Do you want to continue? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo Test cancelled by user.
    call :log_info "Comprehensive test suite cancelled by user"
    exit /b 0
)

set TEST_SCRIPT=scripts\test_backup_system.py
call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Test script not found: %TEST_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --all-tests --verbose" "Comprehensive test execution"
set test_exit_code=!ERRORLEVEL!

call :display_test_result "Comprehensive test suite" %test_exit_code%
call :log_operation_end "All Tests" %test_exit_code%
exit /b %test_exit_code%

:run_quick_test
call :log_info "Running quick test suite..."
echo Running quick test suite...

REM Check if quick test script exists
set QUICK_TEST_SCRIPT=test_cleaned_system.py
if exist "%QUICK_TEST_SCRIPT%" (
    call :safe_execute "%PYTHON_CMD% %QUICK_TEST_SCRIPT% --quick" "Quick test execution"
    set test_exit_code=!ERRORLEVEL!
) else (
    REM Fallback to regular test script with quick mode
    set TEST_SCRIPT=scripts\test_backup_system.py
    call :validate_file_path "%TEST_SCRIPT%" "Test backup script"
    if !ERRORLEVEL! NEQ 0 (
        call :handle_error %ERROR_FILE_NOT_FOUND% "No test script found"
        exit /b %ERROR_FILE_NOT_FOUND%
    )
    
    call :safe_execute "%PYTHON_CMD% %TEST_SCRIPT% --dry-run --single-table" "Quick test execution"
    set test_exit_code=!ERRORLEVEL!
)

call :display_test_result "Quick test suite" %test_exit_code%
call :log_operation_end "Quick Test" %test_exit_code%
exit /b %test_exit_code%

REM ===============================================================
REM TEST UTILITIES
REM ===============================================================

:display_test_result
setlocal
set "test_name=%~1"
set "exit_code=%~2"

echo.
echo ===============================================================
echo TEST RESULT: %test_name%
echo ===============================================================
echo Exit Code: %exit_code%
echo Completion Time: %date% %time%

if "%exit_code%"=="0" (
    echo Status: ✅ PASSED
    call :log_info "Test PASSED: %test_name%"
) else (
    echo Status: ❌ FAILED
    call :log_error "Test FAILED: %test_name% (Exit code: %exit_code%)"
)

echo Log File: %LOG_FILE%
echo ===============================================================

goto :eof

:show_test_help
echo ===============================================================
echo TNGD Backup System Testing Framework - Help
echo ===============================================================
echo.
echo Usage:
echo   run_daily_backup.bat test [test-type] [options]
echo.
echo Test Types:
echo   dry-run           Validate tables only, no backup
echo   single-table      Test backup with one table
echo   full-test [N]     Test complete backup process (limit to N tables)
echo   performance       Run performance benchmarks
echo   error-handling    Test error scenarios
echo   all-tests         Run comprehensive test suite
echo   quick             Run quick test suite
echo   help              Show this help message
echo.
echo Options:
echo   --verbose         Enable detailed logging
echo.
echo Examples:
echo   run_daily_backup.bat test dry-run
echo   run_daily_backup.bat test single-table --verbose
echo   run_daily_backup.bat test full-test 3
echo   run_daily_backup.bat test all-tests
echo.
echo Test Descriptions:
echo   • dry-run: Validates configuration and connectivity without performing backups
echo   • single-table: Tests backup process with a single table
echo   • full-test: Tests complete backup process with limited number of tables
echo   • performance: Measures backup performance and generates benchmarks
echo   • error-handling: Tests system behavior under error conditions
echo   • all-tests: Runs comprehensive test suite covering all scenarios
echo   • quick: Runs essential tests for rapid validation
echo.
echo ===============================================================

call :log_operation_end "Test Help" 0
exit /b 0
