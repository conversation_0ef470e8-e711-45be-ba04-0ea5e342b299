@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Unit Test Framework                             S4NG-7
REM ===============================================================
REM Comprehensive unit testing framework for TNGD backup system
REM - Test execution and reporting
REM - Assertion functions
REM - Mock utilities
REM - Test isolation
REM - Coverage reporting
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Test framework variables
set "TEST_COUNT=0"
set "PASSED_COUNT=0"
set "FAILED_COUNT=0"
set "SKIPPED_COUNT=0"
set "TEST_LOG_FILE="
set "VERBOSE_MODE=false"

REM ===============================================================
REM TEST FRAMEWORK INITIALIZATION
REM ===============================================================

:init_test_framework
REM Initialize test framework
call :get_timestamp timestamp
set "TEST_LOG_DIR=tests\logs\unit_%timestamp%"
if not exist "%TEST_LOG_DIR%" mkdir "%TEST_LOG_DIR%" 2>nul
set "TEST_LOG_FILE=%TEST_LOG_DIR%\unit_tests.log"

echo =============================================================== > "%TEST_LOG_FILE%"
echo TNGD Unit Test Framework - Test Run >> "%TEST_LOG_FILE%"
echo Started: %date% %time% >> "%TEST_LOG_FILE%"
echo =============================================================== >> "%TEST_LOG_FILE%"

echo ===============================================================
echo TNGD UNIT TEST FRAMEWORK
echo ===============================================================
echo Test Log: %TEST_LOG_FILE%
echo Started: %date% %time%
echo ===============================================================
echo.

goto :eof

REM ===============================================================
REM ASSERTION FUNCTIONS
REM ===============================================================

:assert_equals
REM Usage: call :assert_equals "expected" "actual" "test_description"
setlocal
set "expected=%~1"
set "actual=%~2"
set "description=%~3"

call :increment_test_count

if "%expected%"=="%actual%" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "Expected: '%expected%', Actual: '%actual%'"
    exit /b 1
)

:assert_not_equals
REM Usage: call :assert_not_equals "not_expected" "actual" "test_description"
setlocal
set "not_expected=%~1"
set "actual=%~2"
set "description=%~3"

call :increment_test_count

if not "%not_expected%"=="%actual%" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "Expected NOT: '%not_expected%', but got: '%actual%'"
    exit /b 1
)

:assert_true
REM Usage: call :assert_true condition "test_description"
setlocal
set "condition=%~1"
set "description=%~2"

call :increment_test_count

if "%condition%"=="true" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "Expected: true, Actual: %condition%"
    exit /b 1
)

:assert_false
REM Usage: call :assert_false condition "test_description"
setlocal
set "condition=%~1"
set "description=%~2"

call :increment_test_count

if "%condition%"=="false" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "Expected: false, Actual: %condition%"
    exit /b 1
)

:assert_file_exists
REM Usage: call :assert_file_exists "file_path" "test_description"
setlocal
set "file_path=%~1"
set "description=%~2"

call :increment_test_count

if exist "%file_path%" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "File does not exist: %file_path%"
    exit /b 1
)

:assert_file_not_exists
REM Usage: call :assert_file_not_exists "file_path" "test_description"
setlocal
set "file_path=%~1"
set "description=%~2"

call :increment_test_count

if not exist "%file_path%" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "File should not exist: %file_path%"
    exit /b 1
)

:assert_exit_code
REM Usage: call :assert_exit_code expected_code actual_code "test_description"
setlocal
set "expected_code=%~1"
set "actual_code=%~2"
set "description=%~3"

call :increment_test_count

if "%expected_code%"=="%actual_code%" (
    call :test_passed "%description%"
    exit /b 0
) else (
    call :test_failed "%description%" "Expected exit code: %expected_code%, Actual: %actual_code%"
    exit /b 1
)

REM ===============================================================
REM TEST EXECUTION FUNCTIONS
REM ===============================================================

:run_test_suite
REM Usage: call :run_test_suite "test_suite_name"
setlocal
set "suite_name=%~1"

echo.
echo ===============================================================
echo RUNNING TEST SUITE: %suite_name%
echo ===============================================================
call :log_test "Starting test suite: %suite_name%"

REM Route to appropriate test suite
if /i "%suite_name%"=="common_functions" call :test_common_functions
if /i "%suite_name%"=="error_handling" call :test_error_handling
if /i "%suite_name%"=="logging" call :test_logging
if /i "%suite_name%"=="config_manager" call :test_config_manager
if /i "%suite_name%"=="all" call :test_all_suites

echo ===============================================================
echo COMPLETED TEST SUITE: %suite_name%
echo ===============================================================

goto :eof

:test_all_suites
call :test_common_functions
call :test_error_handling
call :test_logging
call :test_config_manager
goto :eof

REM ===============================================================
REM COMMON FUNCTIONS TESTS
REM ===============================================================

:test_common_functions
echo.
echo --- Testing Common Functions ---

REM Load the module to test
call "bin\shared\common_functions.bat"

REM Test parameter validation
call :test_validate_required_param
call :test_validate_safe_string
call :test_validate_file_path
call :test_get_timestamp
call :test_ensure_directory

goto :eof

:test_validate_required_param
echo Testing validate_required_param...

REM Test with valid parameter
call :validate_required_param "test_value" "test_param" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "validate_required_param with valid parameter"

REM Test with empty parameter
call :validate_required_param "" "test_param" >nul 2>&1
call :assert_exit_code "1" "!ERRORLEVEL!" "validate_required_param with empty parameter"

goto :eof

:test_validate_safe_string
echo Testing validate_safe_string...

REM Test with safe string
call :validate_safe_string "safe_string_123" "test_param" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "validate_safe_string with safe string"

REM Test with dangerous characters
call :validate_safe_string "dangerous&string" "test_param" >nul 2>&1
call :assert_exit_code "1" "!ERRORLEVEL!" "validate_safe_string with dangerous characters"

goto :eof

:test_validate_file_path
echo Testing validate_file_path...

REM Create a temporary test file
echo test > "temp_test_file.txt"

REM Test with existing file
call :validate_file_path "temp_test_file.txt" "test file" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "validate_file_path with existing file"

REM Test with non-existing file
call :validate_file_path "non_existing_file.txt" "test file" >nul 2>&1
call :assert_exit_code "1" "!ERRORLEVEL!" "validate_file_path with non-existing file"

REM Cleanup
del "temp_test_file.txt" 2>nul

goto :eof

:test_get_timestamp
echo Testing get_timestamp...

call :get_timestamp test_timestamp
call :assert_true "!test_timestamp!" "get_timestamp returns non-empty value"

REM Check format (basic validation)
echo !test_timestamp! | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]_[0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
call :assert_exit_code "0" "!ERRORLEVEL!" "get_timestamp returns correct format"

goto :eof

:test_ensure_directory
echo Testing ensure_directory...

set "test_dir=test_temp_directory"

REM Remove directory if it exists
if exist "%test_dir%" rmdir /s /q "%test_dir%" 2>nul

REM Test directory creation
call :ensure_directory "%test_dir%" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "ensure_directory creates directory"
call :assert_file_exists "%test_dir%" "ensure_directory - directory exists after creation"

REM Test with existing directory
call :ensure_directory "%test_dir%" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "ensure_directory handles existing directory"

REM Cleanup
if exist "%test_dir%" rmdir /s /q "%test_dir%" 2>nul

goto :eof

REM ===============================================================
REM ERROR HANDLING TESTS
REM ===============================================================

:test_error_handling
echo.
echo --- Testing Error Handling ---

REM Load the module to test
call "bin\shared\error_handling.bat"

call :test_error_codes
call :test_safe_execute

goto :eof

:test_error_codes
echo Testing error code definitions...

call :assert_equals "0" "%ERROR_SUCCESS%" "ERROR_SUCCESS code"
call :assert_equals "1" "%ERROR_GENERAL%" "ERROR_GENERAL code"
call :assert_equals "2" "%ERROR_INVALID_PARAM%" "ERROR_INVALID_PARAM code"

goto :eof

:test_safe_execute
echo Testing safe_execute...

REM Test successful command
call :safe_execute "echo test" "test command" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "safe_execute with successful command"

REM Test failing command
call :safe_execute "exit /b 1" "failing command" >nul 2>&1
call :assert_exit_code "1" "!ERRORLEVEL!" "safe_execute with failing command"

goto :eof

REM ===============================================================
REM TEST UTILITIES
REM ===============================================================

:increment_test_count
set /a "TEST_COUNT+=1"
goto :eof

:test_passed
set "description=%~1"
set /a "PASSED_COUNT+=1"
echo ✅ PASS: %description%
call :log_test "PASS: %description%"
goto :eof

:test_failed
set "description=%~1"
set "details=%~2"
set /a "FAILED_COUNT+=1"
echo ❌ FAIL: %description%
if not "%details%"=="" echo    Details: %details%
call :log_test "FAIL: %description% - %details%"
goto :eof

:test_skipped
set "description=%~1"
set "reason=%~2"
set /a "SKIPPED_COUNT+=1"
echo ⏭️ SKIP: %description%
if not "%reason%"=="" echo    Reason: %reason%
call :log_test "SKIP: %description% - %reason%"
goto :eof

:log_test
set "message=%~1"
echo [%date% %time%] %message% >> "%TEST_LOG_FILE%"
if "%VERBOSE_MODE%"=="true" echo [LOG] %message%
goto :eof

:get_timestamp
REM Get timestamp in YYYY-MM-DD_HH-MM-SS format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "%dt%"=="" (
    set "timestamp=%date:~-4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
) else (
    set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"
)
set "%~1=%timestamp%"
goto :eof

:show_test_results
echo.
echo ===============================================================
echo TEST RESULTS SUMMARY
echo ===============================================================
echo Total Tests: %TEST_COUNT%
echo Passed: %PASSED_COUNT%
echo Failed: %FAILED_COUNT%
echo Skipped: %SKIPPED_COUNT%
echo.

if %FAILED_COUNT% EQU 0 (
    echo ✅ ALL TESTS PASSED!
    set "overall_result=0"
) else (
    echo ❌ %FAILED_COUNT% TESTS FAILED
    set "overall_result=1"
)

echo.
echo Test Log: %TEST_LOG_FILE%
echo ===============================================================

call :log_test "Test run completed - Total: %TEST_COUNT%, Passed: %PASSED_COUNT%, Failed: %FAILED_COUNT%, Skipped: %SKIPPED_COUNT%"

exit /b %overall_result%
