@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Performance Benchmark Suite                     S4NG-7
REM ===============================================================
REM Comprehensive performance testing for TNGD backup system
REM - Execution time measurements
REM - Memory usage monitoring
REM - Throughput analysis
REM - Scalability testing
REM - Performance regression detection
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Performance test variables
set "PERF_LOG_DIR=tests\logs\performance"
set "BENCHMARK_RESULTS_FILE="
set "ITERATIONS=5"
set "WARMUP_ITERATIONS=2"

REM ===============================================================
REM MAIN PERFORMANCE TESTING
REM ===============================================================

:main_performance
call :init_performance_testing

echo ===============================================================
echo TNGD PERFORMANCE BENCHMARK SUITE
echo ===============================================================
echo.

REM Process command line arguments
set "BENCHMARK_TYPE=all"
set "GENERATE_REPORT=true"

:parse_perf_args
if "%~1"=="" goto perf_args_done

if /i "%~1"=="--type" (
    if not "%~2"=="" (
        set "BENCHMARK_TYPE=%~2"
        shift & shift & goto parse_perf_args
    )
)

if /i "%~1"=="--iterations" (
    if not "%~2"=="" (
        set "ITERATIONS=%~2"
        shift & shift & goto parse_perf_args
    )
)

if /i "%~1"=="--no-report" (
    set "GENERATE_REPORT=false"
    shift & goto parse_perf_args
)

if /i "%~1"=="--help" goto show_performance_help

shift & goto parse_perf_args

:perf_args_done

echo Running %BENCHMARK_TYPE% performance benchmarks with %ITERATIONS% iterations...
echo.

REM Execute benchmarks based on type
if /i "%BENCHMARK_TYPE%"=="all" call :run_all_benchmarks
if /i "%BENCHMARK_TYPE%"=="startup" call :benchmark_startup_performance
if /i "%BENCHMARK_TYPE%"=="modules" call :benchmark_module_performance
if /i "%BENCHMARK_TYPE%"=="validation" call :benchmark_validation_performance
if /i "%BENCHMARK_TYPE%"=="logging" call :benchmark_logging_performance

REM Generate performance report
if "%GENERATE_REPORT%"=="true" call :generate_performance_report

echo.
echo Performance benchmarks completed!
echo Results saved to: %BENCHMARK_RESULTS_FILE%

exit /b 0

REM ===============================================================
REM PERFORMANCE TEST INITIALIZATION
REM ===============================================================

:init_performance_testing
REM Initialize performance testing environment
call :get_timestamp timestamp
set "PERF_LOG_DIR=tests\logs\performance_%timestamp%"
if not exist "%PERF_LOG_DIR%" mkdir "%PERF_LOG_DIR%" 2>nul

set "BENCHMARK_RESULTS_FILE=%PERF_LOG_DIR%\benchmark_results.txt"

echo =============================================================== > "%BENCHMARK_RESULTS_FILE%"
echo TNGD Performance Benchmark Results >> "%BENCHMARK_RESULTS_FILE%"
echo Started: %date% %time% >> "%BENCHMARK_RESULTS_FILE%"
echo Iterations: %ITERATIONS% >> "%BENCHMARK_RESULTS_FILE%"
echo =============================================================== >> "%BENCHMARK_RESULTS_FILE%"
echo. >> "%BENCHMARK_RESULTS_FILE%"

goto :eof

REM ===============================================================
REM BENCHMARK SUITES
REM ===============================================================

:run_all_benchmarks
call :benchmark_startup_performance
call :benchmark_module_performance
call :benchmark_validation_performance
call :benchmark_logging_performance
goto :eof

:benchmark_startup_performance
echo.
echo --- Benchmarking Startup Performance ---

REM Benchmark main entry point startup
call :benchmark_command "Main Entry Point Help" "bin\run_daily_backup_new.bat help"

REM Benchmark module loading
call :benchmark_command "Load Common Functions" "call bin\shared\common_functions.bat"
call :benchmark_command "Load Error Handling" "call bin\shared\error_handling.bat"
call :benchmark_command "Load Logging" "call bin\shared\logging.bat"
call :benchmark_command "Load Config Manager" "call bin\shared\config_manager.bat"

goto :eof

:benchmark_module_performance
echo.
echo --- Benchmarking Module Performance ---

REM Benchmark backup module initialization
call :benchmark_command "Daily Backup Help" "bin\backup\daily_backup.bat --help"
call :benchmark_command "Single Table Help" "bin\backup\single_table_backup.bat --help"

REM Benchmark test module
call :benchmark_command "Test Runner Help" "bin\test\test_runner.bat help"

REM Benchmark setup module
call :benchmark_command "Setup Help" "bin\setup\scheduler_setup.bat help"

goto :eof

:benchmark_validation_performance
echo.
echo --- Benchmarking Validation Performance ---

REM Load common functions for validation testing
call "bin\shared\common_functions.bat"

REM Benchmark parameter validation functions
call :benchmark_validation_function "validate_required_param" "test_value" "test_param"
call :benchmark_validation_function "validate_safe_string" "safe_string_123" "test_param"

REM Create temporary file for file validation benchmark
echo test > "temp_perf_test.txt"
call :benchmark_validation_function "validate_file_path" "temp_perf_test.txt" "test file"
del "temp_perf_test.txt" 2>nul

goto :eof

:benchmark_logging_performance
echo.
echo --- Benchmarking Logging Performance ---

REM Load logging module
call "bin\shared\logging.bat"

REM Setup test logging
set "perf_log_file=%PERF_LOG_DIR%\perf_test.log"
call :init_logging "%perf_log_file%" "PerfTest" 20

REM Benchmark logging functions
call :benchmark_logging_function "log_info" "Performance test info message"
call :benchmark_logging_function "log_warning" "Performance test warning message"
call :benchmark_logging_function "log_error" "Performance test error message"

goto :eof

REM ===============================================================
REM BENCHMARK EXECUTION FUNCTIONS
REM ===============================================================

:benchmark_command
REM Usage: call :benchmark_command "test_name" "command"
setlocal enabledelayedexpansion
set "test_name=%~1"
set "command=%~2"

echo Benchmarking: %test_name%

REM Warmup iterations
echo   Warming up...
for /L %%i in (1,1,%WARMUP_ITERATIONS%) do (
    %command% >nul 2>&1
)

REM Actual benchmark iterations
echo   Running %ITERATIONS% iterations...
set "total_time=0"
set "successful_runs=0"

for /L %%i in (1,1,%ITERATIONS%) do (
    call :measure_command_time "%command%" iteration_time
    if !ERRORLEVEL! EQU 0 (
        set /a "successful_runs+=1"
    )
)

REM Calculate average (simplified)
if %successful_runs% GTR 0 (
    echo   ✅ Completed %successful_runs%/%ITERATIONS% successful runs
) else (
    echo   ❌ No successful runs
)

REM Log results
echo %test_name%: %successful_runs%/%ITERATIONS% successful runs >> "%BENCHMARK_RESULTS_FILE%"

goto :eof

:benchmark_validation_function
REM Usage: call :benchmark_validation_function "function_name" "param1" "param2"
setlocal enabledelayedexpansion
set "function_name=%~1"
set "param1=%~2"
set "param2=%~3"

echo Benchmarking validation: %function_name%

set "successful_runs=0"

for /L %%i in (1,1,%ITERATIONS%) do (
    call :%function_name% "%param1%" "%param2%" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        set /a "successful_runs+=1"
    )
)

echo   ✅ %function_name%: %successful_runs%/%ITERATIONS% successful runs
echo %function_name%: %successful_runs%/%ITERATIONS% successful runs >> "%BENCHMARK_RESULTS_FILE%"

goto :eof

:benchmark_logging_function
REM Usage: call :benchmark_logging_function "function_name" "message"
setlocal enabledelayedexpansion
set "function_name=%~1"
set "message=%~2"

echo Benchmarking logging: %function_name%

set "successful_runs=0"

for /L %%i in (1,1,%ITERATIONS%) do (
    call :%function_name% "%message%" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        set /a "successful_runs+=1"
    )
)

echo   ✅ %function_name%: %successful_runs%/%ITERATIONS% successful runs
echo %function_name%: %successful_runs%/%ITERATIONS% successful runs >> "%BENCHMARK_RESULTS_FILE%"

goto :eof

:measure_command_time
REM Usage: call :measure_command_time "command" result_var
REM Simple time measurement (for more precise timing, use external tools)
setlocal
set "command=%~1"

REM Execute command
%command% >nul 2>&1
set "exit_code=!ERRORLEVEL!"

endlocal & set "%~2=%exit_code%"
goto :eof

REM ===============================================================
REM PERFORMANCE ANALYSIS
REM ===============================================================

:generate_performance_report
echo.
echo Generating performance report...

set "report_file=%PERF_LOG_DIR%\performance_report.html"

echo ^<!DOCTYPE html^> > "%report_file%"
echo ^<html^> >> "%report_file%"
echo ^<head^> >> "%report_file%"
echo ^<title^>TNGD Performance Benchmark Report^</title^> >> "%report_file%"
echo ^<style^> >> "%report_file%"
echo body { font-family: Arial, sans-serif; margin: 20px; } >> "%report_file%"
echo .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; } >> "%report_file%"
echo .benchmark { background-color: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 3px; } >> "%report_file%"
echo .good { color: green; } >> "%report_file%"
echo .warning { color: orange; } >> "%report_file%"
echo .poor { color: red; } >> "%report_file%"
echo ^</style^> >> "%report_file%"
echo ^</head^> >> "%report_file%"
echo ^<body^> >> "%report_file%"

echo ^<div class="header"^> >> "%report_file%"
echo ^<h1^>TNGD Performance Benchmark Report^</h1^> >> "%report_file%"
echo ^<p^>Generated: %date% %time%^</p^> >> "%report_file%"
echo ^<p^>Benchmark Type: %BENCHMARK_TYPE%^</p^> >> "%report_file%"
echo ^<p^>Iterations: %ITERATIONS%^</p^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

echo ^<h2^>Benchmark Results^</h2^> >> "%report_file%"
echo ^<div class="benchmark"^> >> "%report_file%"
echo ^<pre^> >> "%report_file%"

REM Include benchmark results
if exist "%BENCHMARK_RESULTS_FILE%" (
    type "%BENCHMARK_RESULTS_FILE%" >> "%report_file%"
)

echo ^</pre^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

echo ^<h2^>Performance Analysis^</h2^> >> "%report_file%"
echo ^<p^>This report shows the execution success rates for various TNGD components.^</p^> >> "%report_file%"
echo ^<p^>Success rates of 100%% indicate reliable performance.^</p^> >> "%report_file%"
echo ^<p^>Lower success rates may indicate performance issues or environmental problems.^</p^> >> "%report_file%"

echo ^</body^> >> "%report_file%"
echo ^</html^> >> "%report_file%"

echo ✅ Performance report generated: %report_file%

goto :eof

REM ===============================================================
REM UTILITIES
REM ===============================================================

:get_timestamp
REM Get timestamp in YYYY-MM-DD_HH-MM-SS format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "%dt%"=="" (
    set "timestamp=%date:~-4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
) else (
    set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"
)
set "%~1=%timestamp%"
goto :eof

:show_performance_help
echo ===============================================================
echo TNGD Performance Benchmark Suite - Help
echo ===============================================================
echo.
echo Usage:
echo   performance_benchmarks.bat [options]
echo.
echo Options:
echo   --type TYPE           Run specific benchmark type (default: all)
echo   --iterations N        Number of iterations per test (default: 5)
echo   --no-report           Skip HTML report generation
echo   --help                Show this help message
echo.
echo Available Benchmark Types:
echo   all                   Run all performance benchmarks
echo   startup               Test startup and initialization performance
echo   modules               Test module loading and execution performance
echo   validation            Test parameter validation performance
echo   logging               Test logging system performance
echo.
echo Examples:
echo   performance_benchmarks.bat
echo   performance_benchmarks.bat --type startup --iterations 10
echo   performance_benchmarks.bat --no-report
echo.
echo Output:
echo   Results are saved to: tests\logs\performance_TIMESTAMP\
echo   HTML report is generated (unless --no-report is used)
echo.
echo Notes:
echo   • Higher iteration counts provide more accurate results
echo   • Warmup iterations are performed before actual benchmarks
echo   • Success rates indicate reliability, not absolute performance
echo   • For precise timing, consider using external profiling tools
echo.
echo ===============================================================

exit /b 0
