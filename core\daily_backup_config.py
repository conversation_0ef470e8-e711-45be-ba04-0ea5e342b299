#!/usr/bin/env python3
"""
Daily Backup Configuration Module

This module provides configuration management specifically for daily backup operations,
completely separate from monthly backup configurations to prevent confusion and conflicts.

Features:
- Daily-specific configuration validation
- Current/recent date validation
- Daily storage path configuration
- Daily retry and timeout settings
- Daily resource management settings
"""

import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

from core.config_manager import ConfigManager
from utils.minimal_logging import logger


@dataclass
class DailyBackupSettings:
    """Daily backup specific settings."""
    # Target date settings
    target_date: datetime.date
    
    # Processing settings
    max_retries: int = 3
    retry_delay_seconds: int = 30
    timeout_seconds: int = 900  # 15 minutes for daily
    chunk_size: int = 1000
    
    # Resource management
    max_concurrent_tables: int = 3  # Can process multiple tables in parallel for daily
    memory_limit_mb: int = 1024
    disk_space_threshold_gb: int = 5
    
    # Storage settings
    storage_prefix: str = "daily_backup"
    compression_algorithm: str = "tar.gz"
    
    # Daily-specific settings
    incremental: bool = False
    skip_empty_tables: bool = True
    validate_recent_data: bool = True
    
    # Performance settings
    enable_parallel_processing: bool = True
    table_timeout_seconds: int = 300  # 5 minutes per table
    
    def __post_init__(self):
        """Validate settings after initialization."""
        # Validate that target date is not too far in the future
        max_future_days = 1
        if self.target_date > datetime.date.today() + datetime.timedelta(days=max_future_days):
            raise ValueError(f"Target date cannot be more than {max_future_days} days in the future")
        
        # Validate that target date is not too far in the past
        max_past_days = 30
        if self.target_date < datetime.date.today() - datetime.timedelta(days=max_past_days):
            logger.warning(f"Target date {self.target_date} is more than {max_past_days} days in the past")
    
    @property
    def is_today(self) -> bool:
        """Check if target date is today."""
        return self.target_date == datetime.date.today()
    
    @property
    def is_yesterday(self) -> bool:
        """Check if target date is yesterday."""
        return self.target_date == datetime.date.today() - datetime.timedelta(days=1)
    
    @property
    def days_from_today(self) -> int:
        """Get number of days from today (negative for past, positive for future)."""
        return (self.target_date - datetime.date.today()).days


@dataclass
class DailyTableConfig:
    """Configuration for tables in daily backup."""
    table_names: List[str]
    table_config_path: Optional[str] = None
    
    # Table-specific settings
    table_timeout_seconds: int = 300  # 5 minutes per table
    table_retry_count: int = 2
    
    # Data validation
    validate_table_structure: bool = False  # Less validation for daily
    check_data_consistency: bool = False
    
    # Performance settings
    enable_table_parallelism: bool = True
    max_rows_per_chunk: int = 10000
    
    def __post_init__(self):
        """Validate table configuration."""
        if not self.table_names:
            raise ValueError("Table names list cannot be empty")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tables = []
        for table in self.table_names:
            if table not in seen:
                seen.add(table)
                unique_tables.append(table)
        self.table_names = unique_tables
        
        if len(self.table_names) != len(set(self.table_names)):
            logger.warning("Duplicate table names found and removed")


class DailyBackupConfigManager:
    """
    Configuration manager specifically for daily backup operations.
    
    This manager handles all daily backup configuration needs and is
    completely separate from monthly backup configuration management.
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the daily backup configuration manager.
        
        Args:
            config_manager: Optional base configuration manager
        """
        self.config_manager = config_manager or ConfigManager()
        self.daily_config = self.config_manager.get('backup', 'daily_backup', {})
        
        logger.info("DailyBackupConfigManager initialized")
    
    def create_daily_settings(self, target_date: Optional[datetime.date] = None,
                            custom_settings: Optional[Dict[str, Any]] = None) -> DailyBackupSettings:
        """
        Create daily backup settings for the specified date.
        
        Args:
            target_date: Target date (defaults to yesterday)
            custom_settings: Optional custom settings to override defaults
            
        Returns:
            DailyBackupSettings instance
        """
        # Default to yesterday if not specified
        if target_date is None:
            target_date = datetime.date.today() - datetime.timedelta(days=1)
        
        # Start with default settings from config
        settings_dict = {
            'target_date': target_date,
            'max_retries': self.daily_config.get('max_retries', 3),
            'retry_delay_seconds': self.daily_config.get('retry_delay_seconds', 30),
            'timeout_seconds': self.daily_config.get('timeout_seconds', 900),
            'chunk_size': self.daily_config.get('chunk_size', 1000),
            'max_concurrent_tables': self.daily_config.get('max_concurrent_tables', 3),
            'memory_limit_mb': self.daily_config.get('memory_limit_mb', 1024),
            'disk_space_threshold_gb': self.daily_config.get('disk_space_threshold_gb', 5),
            'storage_prefix': self.daily_config.get('storage_prefix', 'daily_backup'),
            'compression_algorithm': self.daily_config.get('compression_algorithm', 'tar.gz'),
            'incremental': self.daily_config.get('incremental', False),
            'skip_empty_tables': self.daily_config.get('skip_empty_tables', True),
            'validate_recent_data': self.daily_config.get('validate_recent_data', True),
            'enable_parallel_processing': self.daily_config.get('enable_parallel_processing', True),
            'table_timeout_seconds': self.daily_config.get('table_timeout_seconds', 300)
        }
        
        # Apply custom settings if provided
        if custom_settings:
            settings_dict.update(custom_settings)
        
        return DailyBackupSettings(**settings_dict)
    
    def create_table_config(self, table_names: Optional[List[str]] = None,
                          table_config_path: Optional[str] = None) -> DailyTableConfig:
        """
        Create table configuration for daily backup.
        
        Args:
            table_names: Optional list of table names
            table_config_path: Optional path to table configuration file
            
        Returns:
            DailyTableConfig instance
        """
        # Load table names if not provided
        if table_names is None:
            table_names = self._load_table_names(table_config_path)
        
        table_settings = self.daily_config.get('table_settings', {})
        
        return DailyTableConfig(
            table_names=table_names,
            table_config_path=table_config_path,
            table_timeout_seconds=table_settings.get('table_timeout_seconds', 300),
            table_retry_count=table_settings.get('table_retry_count', 2),
            validate_table_structure=table_settings.get('validate_table_structure', False),
            check_data_consistency=table_settings.get('check_data_consistency', False),
            enable_table_parallelism=table_settings.get('enable_table_parallelism', True),
            max_rows_per_chunk=table_settings.get('max_rows_per_chunk', 10000)
        )
    
    def _load_table_names(self, config_path: Optional[str] = None) -> List[str]:
        """
        Load table names from configuration file.
        
        Args:
            config_path: Optional path to table configuration file
            
        Returns:
            List of table names
        """
        # Try specified path first
        if config_path:
            table_names = self._load_from_file(config_path)
            if table_names:
                return table_names
        
        # Try default daily table config paths
        default_paths = [
            'tabletest/tables.json',
            'config/daily_tables.json',
            'config/tables.json'
        ]
        
        for path in default_paths:
            table_names = self._load_from_file(path)
            if table_names:
                logger.info(f"Loaded {len(table_names)} tables from {path}")
                return table_names
        
        # Fallback to default tables
        logger.warning("Could not load table configuration, using default tables")
        return ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
    
    def _load_from_file(self, file_path: str) -> Optional[List[str]]:
        """
        Load table names from a specific file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            List of table names or None if failed
        """
        try:
            import json
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'tables' in data:
                return data['tables']
            else:
                logger.warning(f"Invalid table configuration format in {file_path}")
                return None
                
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            logger.debug(f"Could not load table configuration from {file_path}: {str(e)}")
            return None
    
    def generate_storage_path(self, table_name: str, target_date: datetime.date,
                            settings: DailyBackupSettings) -> str:
        """
        Generate storage path for daily backup.
        
        Args:
            table_name: Name of the table
            target_date: Target backup date
            settings: Daily backup settings
            
        Returns:
            Storage path string
        """
        return (f"{settings.storage_prefix}/{target_date.year}/"
                f"{target_date.strftime('%m-%B')}/{target_date.strftime('%Y-%m-%d')}/"
                f"{table_name}_{target_date.strftime('%Y%m%d')}.{settings.compression_algorithm}")
    
    def validate_daily_config(self, settings: DailyBackupSettings,
                            table_config: DailyTableConfig) -> Dict[str, Any]:
        """
        Validate daily backup configuration.
        
        Args:
            settings: Daily backup settings
            table_config: Table configuration
            
        Returns:
            Validation results dictionary
        """
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate target date
        if settings.target_date > datetime.date.today() + datetime.timedelta(days=1):
            validation_results['errors'].append("Target date is too far in the future")
            validation_results['valid'] = False
        
        # Validate table configuration
        if not table_config.table_names:
            validation_results['errors'].append("No tables configured for backup")
            validation_results['valid'] = False
        
        # Validate resource settings
        if settings.memory_limit_mb < 256:
            validation_results['warnings'].append("Memory limit is very low")
        
        if settings.disk_space_threshold_gb < 2:
            validation_results['warnings'].append("Disk space threshold is very low")
        
        # Daily-specific validations
        if settings.is_today:
            validation_results['warnings'].append("Backing up today's data - data may be incomplete")
        
        if settings.days_from_today < -7:
            validation_results['warnings'].append("Backing up data older than 7 days")
        
        return validation_results
