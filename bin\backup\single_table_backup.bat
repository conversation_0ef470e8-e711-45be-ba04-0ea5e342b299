@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Single Table Backup Module                     S4NG-7
REM ===============================================================
REM Focused single table backup functionality
REM - Individual table processing
REM - Flexible parameter handling
REM - Table listing capabilities
REM - Comprehensive validation
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0..\.."

REM Load shared libraries
call "bin\shared\common_functions.bat"
call "bin\shared\error_handling.bat"
call "bin\shared\logging.bat"
call "bin\shared\config_manager.bat"

REM ===============================================================
REM INITIALIZATION
REM ===============================================================

:init_single_table_backup
REM Initialize logging
call :get_timestamp timestamp
call :create_log_directory "single_table" "%timestamp%" log_dir
set "LOG_FILE=%log_dir%\single_table_backup_%timestamp%.log"
call :init_logging "%LOG_FILE%" "SingleTableBackup" %LOG_LEVEL_INFO%

call :log_operation_start "Single Table Backup"

REM Load configuration
call :load_default_config

REM Process command line arguments
call :process_single_table_arguments %*
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

goto :main_single_table_process

REM ===============================================================
REM ARGUMENT PROCESSING
REM ===============================================================

:process_single_table_arguments
set "TABLE_NAME="
set "DAYS_TO_BACKUP=1"
set "LIST_TABLES=false"

REM Check if any arguments provided
if "%~1"=="" (
    call :show_single_table_usage
    exit /b %ERROR_INVALID_PARAM%
)

:parse_single_table_args
if "%~1"=="" goto single_table_args_done

if /i "%~1"=="--list-tables" (
    set LIST_TABLES=true
    call :log_info "List tables mode enabled"
    shift & goto parse_single_table_args
)

if /i "%~1"=="--dry-run" (
    set DRY_RUN=true
    call :log_info "Dry run mode enabled"
    shift & goto parse_single_table_args
)

if /i "%~1"=="--verbose" (
    set VERBOSE=true
    set CURRENT_LOG_LEVEL=%LOG_LEVEL_DEBUG%
    call :log_info "Verbose mode enabled"
    shift & goto parse_single_table_args
)

if /i "%~1"=="--days" (
    if not "%~2"=="" (
        call :validate_safe_string "%~2" "days parameter"
        if !ERRORLEVEL! EQU 0 (
            set DAYS_TO_BACKUP=%~2
            call :log_info "Days to backup set to: %~2"
        )
        shift & shift & goto parse_single_table_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --days"
        exit /b %ERROR_INVALID_PARAM%
    )
)

if /i "%~1"=="--chunk-size" (
    if not "%~2"=="" (
        call :validate_safe_string "%~2" "chunk-size parameter"
        if !ERRORLEVEL! EQU 0 (
            set CHUNK_SIZE=%~2
            call :log_info "Chunk size set to: %~2"
        )
        shift & shift & goto parse_single_table_args
    ) else (
        call :handle_error %ERROR_INVALID_PARAM% "Missing value for --chunk-size"
        exit /b %ERROR_INVALID_PARAM%
    )
)

REM If parameter doesn't start with --, treat as table name
if not "%~1:~0,2%"=="--" (
    if "%TABLE_NAME%"=="" (
        call :validate_safe_string "%~1" "table name"
        if !ERRORLEVEL! EQU 0 (
            set TABLE_NAME=%~1
            call :log_info "Table name set to: %~1"
        ) else (
            call :handle_error %ERROR_INVALID_PARAM% "Invalid table name: %~1"
            exit /b %ERROR_INVALID_PARAM%
        )
    ) else (
        call :log_warning "Multiple table names provided, using first: %TABLE_NAME%"
    )
    shift & goto parse_single_table_args
)

REM Unknown parameter
call :log_warning "Unknown parameter: %~1"
shift & goto parse_single_table_args

:single_table_args_done
goto :eof

REM ===============================================================
REM MAIN SINGLE TABLE PROCESS
REM ===============================================================

:main_single_table_process
call :log_info "Starting single table backup process"

REM Handle list tables request
if "%LIST_TABLES%"=="true" (
    call :list_available_tables
    set list_exit_code=!ERRORLEVEL!
    call :log_operation_end "List Tables" !list_exit_code!
    exit /b !list_exit_code!
)

REM Validate table name is provided
if "%TABLE_NAME%"=="" (
    call :handle_error %ERROR_INVALID_PARAM% "Table name is required"
    call :show_single_table_usage
    exit /b %ERROR_INVALID_PARAM%
)

REM Validate configuration
call :validate_config_parameters
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Configuration parameter validation failed"
    exit /b %ERROR_CONFIG%
)

call :show_current_config

REM Pre-backup checks
call :pre_single_table_checks
if %ERRORLEVEL% NEQ 0 exit /b %ERRORLEVEL%

REM Execute single table backup
call :execute_single_table_backup
set backup_exit_code=!ERRORLEVEL!

REM Generate summary
call :generate_single_table_summary %backup_exit_code%

call :log_operation_end "Single Table Backup" %backup_exit_code%
exit /b %backup_exit_code%

REM ===============================================================
REM TABLE LISTING
REM ===============================================================

:list_available_tables
call :log_info "Listing available tables..."

set SINGLE_TABLE_SCRIPT=bin\backup_single_table.py
call :validate_file_path "%SINGLE_TABLE_SCRIPT%" "Single table backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Single table backup script not found: %SINGLE_TABLE_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

echo.
echo ===============================================================
echo AVAILABLE TABLES
echo ===============================================================

call :safe_execute "%PYTHON_CMD% %SINGLE_TABLE_SCRIPT% --list-tables" "List tables operation"
set list_exit_code=!ERRORLEVEL!

echo ===============================================================

if %list_exit_code% EQU 0 (
    call :log_info "Table listing completed successfully"
) else (
    call :log_error "Table listing failed with exit code: %list_exit_code%"
)

exit /b %list_exit_code%

REM ===============================================================
REM PRE-BACKUP CHECKS
REM ===============================================================

:pre_single_table_checks
call :log_info "Performing pre-backup checks for single table..."

REM Check Python availability
call :safe_execute "call :check_python_available" "Python availability check"
if %ERRORLEVEL% NEQ 0 exit /b %ERROR_PYTHON%

REM Check disk space
call :safe_execute "call :check_disk_space %MIN_DISK_SPACE_GB%" "Disk space check"
if %ERRORLEVEL% NEQ 0 (
    call :log_warning "Low disk space detected"
    REM For single table, we can proceed with less space
)

REM Validate single table backup script exists
set SINGLE_TABLE_SCRIPT=bin\backup_single_table.py
call :validate_file_path "%SINGLE_TABLE_SCRIPT%" "Single table backup script"
if %ERRORLEVEL% NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Single table backup script not found: %SINGLE_TABLE_SCRIPT%"
    exit /b %ERROR_FILE_NOT_FOUND%
)

call :log_info "Pre-backup checks completed successfully"
goto :eof

REM ===============================================================
REM BACKUP EXECUTION
REM ===============================================================

:execute_single_table_backup
call :log_info "Executing single table backup for: %TABLE_NAME%"

REM Build parameters
set "backup_params=%TABLE_NAME%"
if "%DRY_RUN%"=="true" set "backup_params=%backup_params% --dry-run"
if "%VERBOSE%"=="true" set "backup_params=%backup_params% --verbose"
if not "%DAYS_TO_BACKUP%"=="1" set "backup_params=%backup_params% --days %DAYS_TO_BACKUP%"
if not "%CHUNK_SIZE%"=="%DEFAULT_CHUNK_SIZE%" set "backup_params=%backup_params% --chunk-size %CHUNK_SIZE%"

call :log_info "Running: %PYTHON_CMD% %SINGLE_TABLE_SCRIPT% %backup_params%"

echo.
echo ===============================================================
echo 🔄 STARTING SINGLE TABLE BACKUP
echo ===============================================================
echo Table: %TABLE_NAME%
echo Days: %DAYS_TO_BACKUP%
echo Chunk Size: %CHUNK_SIZE%
if "%DRY_RUN%"=="true" echo Mode: DRY RUN
echo ===============================================================
echo.

REM Execute backup with retry mechanism
call :safe_execute_with_retry "%PYTHON_CMD% %SINGLE_TABLE_SCRIPT% %backup_params%" "Single table backup execution" %RETRY_COUNT%
set backup_exit_code=!ERRORLEVEL!

echo.
echo ===============================================================
echo 📊 BACKUP COMPLETED
echo ===============================================================

if %backup_exit_code% EQU 0 (
    call :log_info "Single table backup completed successfully"
    echo ✅ SUCCESS: Table backup completed successfully!
    echo.
    echo 📋 What happened:
    echo   • Table data extracted and compressed
    echo   • Backup uploaded to OSS storage
    echo   • Temporary files cleaned up
    echo   • Process completed without errors
) else (
    call :log_error "Single table backup failed with exit code: %backup_exit_code%"
    echo ❌ FAILURE: Table backup encountered errors
    echo.
    echo 📋 Troubleshooting:
    echo   • Check if the table name is correct
    echo   • Verify database connectivity
    echo   • Review log files for detailed error information
    echo   • Try running with --dry-run to test connectivity
    echo.
    echo 🔧 Common solutions:
    echo   • Use --list-tables to see available tables
    echo   • Check network connectivity to database
    echo   • Verify OSS credentials and permissions
)

echo ===============================================================

exit /b %backup_exit_code%

REM ===============================================================
REM SUMMARY AND USAGE
REM ===============================================================

:generate_single_table_summary
setlocal
set "exit_code=%~1"

call :log_info "Generating single table backup summary..."

echo.
echo ===============================================================
echo SINGLE TABLE BACKUP SUMMARY
echo ===============================================================
echo Table: %TABLE_NAME%
echo Days Processed: %DAYS_TO_BACKUP%
echo Completion Time: %date% %time%
echo Exit Code: %exit_code%
echo Log File: %LOG_FILE%

if "%exit_code%"=="0" (
    echo Status: ✅ SUCCESS
) else (
    echo Status: ❌ FAILED
)

echo ===============================================================

goto :eof

:show_single_table_usage
echo.
echo ===============================================================
echo TNGD SINGLE TABLE BACKUP UTILITY
echo ===============================================================
echo.
echo Usage: run_daily_backup.bat single-table ^<table_name^> [options]
echo.
echo Examples:
echo   run_daily_backup.bat single-table my.app.tngd.waf
echo   run_daily_backup.bat single-table my.app.tngd.waf --days 7 --chunk-size 50000
echo   run_daily_backup.bat single-table my.app.tngd.waf --dry-run
echo   run_daily_backup.bat single-table --list-tables
echo.
echo Available options:
echo   --list-tables     List all available tables
echo   --dry-run         Test mode - validate only, no backup
echo   --days N          Number of days to backup (default: 1)
echo   --chunk-size N    Chunk size for processing (default: %DEFAULT_CHUNK_SIZE%)
echo   --verbose         Enable detailed logging
echo.
echo ===============================================================
goto :eof
