@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Unit Test Runner                                S4NG-7
REM ===============================================================
REM Main entry point for running unit tests
REM - Execute individual test suites
REM - Generate comprehensive reports
REM - Support for different test modes
REM - CI/CD integration support
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Load test framework
call "tests\unit\test_framework.bat"

REM ===============================================================
REM MAIN EXECUTION
REM ===============================================================

:main
call :init_test_framework

REM Process command line arguments
set "TEST_SUITE=all"
set "VERBOSE_MODE=false"
set "GENERATE_REPORT=true"

:parse_args
if "%~1"=="" goto args_done

if /i "%~1"=="--suite" (
    if not "%~2"=="" (
        set "TEST_SUITE=%~2"
        shift & shift & goto parse_args
    )
)

if /i "%~1"=="--verbose" (
    set "VERBOSE_MODE=true"
    shift & goto parse_args
)

if /i "%~1"=="--no-report" (
    set "GENERATE_REPORT=false"
    shift & goto parse_args
)

if /i "%~1"=="--help" goto show_help

shift & goto parse_args

:args_done

REM Execute tests
echo Running unit tests for suite: %TEST_SUITE%
echo.

call :run_test_suite "%TEST_SUITE%"

REM Show results
call :show_test_results

REM Generate report if requested
if "%GENERATE_REPORT%"=="true" (
    call :generate_html_report
)

exit /b %overall_result%

REM ===============================================================
REM ADDITIONAL TEST SUITES
REM ===============================================================

:test_logging
echo.
echo --- Testing Logging Functions ---

REM Load the logging module
call "bin\shared\logging.bat"

call :test_log_levels
call :test_log_formatting
call :test_log_file_creation

goto :eof

:test_log_levels
echo Testing log levels...

REM Test log level constants
call :assert_equals "10" "%LOG_LEVEL_DEBUG%" "LOG_LEVEL_DEBUG constant"
call :assert_equals "20" "%LOG_LEVEL_INFO%" "LOG_LEVEL_INFO constant"
call :assert_equals "30" "%LOG_LEVEL_WARNING%" "LOG_LEVEL_WARNING constant"
call :assert_equals "40" "%LOG_LEVEL_ERROR%" "LOG_LEVEL_ERROR constant"

goto :eof

:test_log_formatting
echo Testing log formatting...

REM Test basic logging functions exist
set "test_log_file=tests\temp_test.log"
call :init_logging "%test_log_file%" "TestComponent" %LOG_LEVEL_INFO%

REM Test info logging
call :log_info "Test info message"
call :assert_file_exists "%test_log_file%" "Log file created after logging"

REM Cleanup
if exist "%test_log_file%" del "%test_log_file%" 2>nul

goto :eof

:test_log_file_creation
echo Testing log file creation...

set "test_log_dir=tests\temp_logs"
set "test_log_file=%test_log_dir%\test.log"

REM Remove test directory if exists
if exist "%test_log_dir%" rmdir /s /q "%test_log_dir%" 2>nul

call :init_logging "%test_log_file%" "TestComponent" %LOG_LEVEL_INFO%
call :assert_file_exists "%test_log_dir%" "Log directory created"
call :assert_file_exists "%test_log_file%" "Log file created"

REM Cleanup
if exist "%test_log_dir%" rmdir /s /q "%test_log_dir%" 2>nul

goto :eof

:test_config_manager
echo.
echo --- Testing Configuration Manager ---

REM Load the config manager module
call "bin\shared\config_manager.bat"

call :test_default_config
call :test_parameter_validation

goto :eof

:test_default_config
echo Testing default configuration...

call :load_default_config

REM Test that defaults are loaded
call :assert_equals "100000" "%DEFAULT_CHUNK_SIZE%" "Default chunk size"
call :assert_equals "1800" "%DEFAULT_TIMEOUT%" "Default timeout"
call :assert_equals "3" "%DEFAULT_RETRY_COUNT%" "Default retry count"

goto :eof

:test_parameter_validation
echo Testing parameter validation...

REM Test numeric parameter validation
set "CHUNK_SIZE=50000"
call :validate_numeric_param "CHUNK_SIZE" "100000" "1000" "10000000" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "validate_numeric_param with valid value"

REM Test boolean parameter validation
set "DRY_RUN=true"
call :validate_boolean_param "DRY_RUN" >nul 2>&1
call :assert_exit_code "0" "!ERRORLEVEL!" "validate_boolean_param with valid boolean"

goto :eof

REM ===============================================================
REM REPORT GENERATION
REM ===============================================================

:generate_html_report
echo.
echo Generating HTML test report...

set "report_file=%TEST_LOG_DIR%\test_report.html"

echo ^<!DOCTYPE html^> > "%report_file%"
echo ^<html^> >> "%report_file%"
echo ^<head^> >> "%report_file%"
echo ^<title^>TNGD Unit Test Report^</title^> >> "%report_file%"
echo ^<style^> >> "%report_file%"
echo body { font-family: Arial, sans-serif; margin: 20px; } >> "%report_file%"
echo .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; } >> "%report_file%"
echo .pass { color: green; } >> "%report_file%"
echo .fail { color: red; } >> "%report_file%"
echo .skip { color: orange; } >> "%report_file%"
echo .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; } >> "%report_file%"
echo ^</style^> >> "%report_file%"
echo ^</head^> >> "%report_file%"
echo ^<body^> >> "%report_file%"

echo ^<div class="header"^> >> "%report_file%"
echo ^<h1^>TNGD Unit Test Report^</h1^> >> "%report_file%"
echo ^<p^>Generated: %date% %time%^</p^> >> "%report_file%"
echo ^<p^>Test Suite: %TEST_SUITE%^</p^> >> "%report_file%"
echo ^</div^> >> "%report_file%"

echo ^<div class="summary"^> >> "%report_file%"
echo ^<h2^>Summary^</h2^> >> "%report_file%"
echo ^<p^>Total Tests: %TEST_COUNT%^</p^> >> "%report_file%"
echo ^<p class="pass"^>Passed: %PASSED_COUNT%^</p^> >> "%report_file%"
echo ^<p class="fail"^>Failed: %FAILED_COUNT%^</p^> >> "%report_file%"
echo ^<p class="skip"^>Skipped: %SKIPPED_COUNT%^</p^> >> "%report_file%"

if %FAILED_COUNT% EQU 0 (
    echo ^<p class="pass"^>^<strong^>Overall Result: PASSED^</strong^>^</p^> >> "%report_file%"
) else (
    echo ^<p class="fail"^>^<strong^>Overall Result: FAILED^</strong^>^</p^> >> "%report_file%"
)

echo ^</div^> >> "%report_file%"

echo ^<h2^>Test Details^</h2^> >> "%report_file%"
echo ^<pre^> >> "%report_file%"

REM Include test log content
if exist "%TEST_LOG_FILE%" (
    type "%TEST_LOG_FILE%" >> "%report_file%"
)

echo ^</pre^> >> "%report_file%"
echo ^</body^> >> "%report_file%"
echo ^</html^> >> "%report_file%"

echo ✅ HTML report generated: %report_file%

goto :eof

REM ===============================================================
REM HELP AND USAGE
REM ===============================================================

:show_help
echo ===============================================================
echo TNGD Unit Test Runner - Help
echo ===============================================================
echo.
echo Usage:
echo   run_unit_tests.bat [options]
echo.
echo Options:
echo   --suite SUITE_NAME    Run specific test suite (default: all)
echo   --verbose             Enable verbose output
echo   --no-report           Skip HTML report generation
echo   --help                Show this help message
echo.
echo Available Test Suites:
echo   all                   Run all test suites
echo   common_functions      Test common utility functions
echo   error_handling        Test error handling functions
echo   logging               Test logging functions
echo   config_manager        Test configuration management
echo.
echo Examples:
echo   run_unit_tests.bat
echo   run_unit_tests.bat --suite common_functions --verbose
echo   run_unit_tests.bat --no-report
echo.
echo Output:
echo   Test logs are saved to: tests\logs\unit_TIMESTAMP\
echo   HTML report is generated (unless --no-report is used)
echo.
echo Exit Codes:
echo   0 = All tests passed
echo   1 = One or more tests failed
echo.
echo ===============================================================

exit /b 0

REM ===============================================================
REM MOCK UTILITIES FOR TESTING
REM ===============================================================

:create_mock_config
REM Create a temporary configuration file for testing
set "mock_config_file=tests\temp_tables.json"
echo [ > "%mock_config_file%"
echo   "test.table.1", >> "%mock_config_file%"
echo   "test.table.2", >> "%mock_config_file%"
echo   "test.table.3" >> "%mock_config_file%"
echo ] >> "%mock_config_file%"
goto :eof

:cleanup_mock_files
REM Clean up temporary test files
if exist "tests\temp_tables.json" del "tests\temp_tables.json" 2>nul
if exist "tests\temp_test.log" del "tests\temp_test.log" 2>nul
if exist "tests\temp_logs" rmdir /s /q "tests\temp_logs" 2>nul
goto :eof

REM ===============================================================
REM PERFORMANCE TESTING UTILITIES
REM ===============================================================

:measure_execution_time
REM Usage: call :measure_execution_time "command" result_var
setlocal enabledelayedexpansion
set "command=%~1"

REM Get start time
call :get_timestamp start_time

REM Execute command
%command%
set "exit_code=!ERRORLEVEL!"

REM Get end time
call :get_timestamp end_time

REM Simple duration calculation (for more precise timing, use external tools)
echo Start: !start_time!, End: !end_time!, Exit Code: !exit_code!

endlocal & set "%~2=!exit_code!"
goto :eof
